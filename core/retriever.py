from typing import List, Dict, Any
from langchain.schema import BaseRetriever, Document
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from pydantic import Field
from agno.models.message import Message
from core.reranker import AzureMLReranker

class AsyncHybridRetriever(BaseRetriever):
    """Hybrid dense + sparse retrieval with query expansion and reranking."""
    
    # Pydantic model configuration
    class Config:
        arbitrary_types_allowed = True
        extra = "allow"
    
    # Define fields properly for Pydantic
    azure_model: Any = Field(default=None)
    vector_store: Any = Field(default=None)
    qdrant_client: Any = Field(default=None)
    collection_name: str = Field(default="")
    reranker: Any = Field(default=None)
    k: int = Field(default=5)
    research_context: Dict[str, Any] = Field(default_factory=lambda: {
        "phase": "foundational",
        "previous_queries": [],
        "cumulative_context": ""
    })
    
    def __init__(self, azure_model, vector_store: QdrantVectorStore, qdrant_client: QdrantClient, 
                 collection_name: str, reranker: AzureMLReranker, k: int = 5, **kwargs):
        super().__init__(
            azure_model=azure_model,
            vector_store=vector_store,
            qdrant_client=qdrant_client,
            collection_name=collection_name,
            reranker=reranker,
            k=k,
            research_context={
                "phase": "foundational",
                "previous_queries": [],
                "cumulative_context": ""
            },
            **kwargs
        )

    def set_research_phase(self, phase: str):
        """Set current research phase for context tracking."""
        self.research_context["phase"] = phase
        if phase == "foundational":
            self.research_context["previous_queries"] = []
            self.research_context["cumulative_context"] = ""

    def _invoke_model(self, prompt: str) -> str:
        """Helper to invoke Azure model with proper message formatting"""
        response = self.azure_model.invoke([Message(role="user", content=prompt)])
        # Extract content from ChatCompletion response
        return response.choices[0].message.content.strip()

    def _get_relevant_documents(self, query: str) -> List[Document]:
        """Simplified synchronous retrieval to avoid threading conflicts."""
        # Simple dense retrieval without complex threading
        dense_retriever = self.vector_store.as_retriever(search_kwargs={"k": self.k})
        docs = dense_retriever.invoke(query)
        
        # Update research context
        self.research_context["previous_queries"].append(query)
        if len(self.research_context["previous_queries"]) > 3:
            self.research_context["previous_queries"] = self.research_context["previous_queries"][-3:]
        
        return docs[:self.k]
    
    async def _aget_relevant_documents(self, query: str, *, run_manager: Any = None) -> List[Document]:
        """Asynchronous retrieval from the vector store."""
        # Simple dense retrieval without complex threading
        dense_retriever = self.vector_store.as_retriever(search_kwargs={"k": self.k})
        # Use the async `ainvoke` method
        docs = await dense_retriever.ainvoke(query)
        
        # Update research context
        self.research_context["previous_queries"].append(query)
        if len(self.research_context["previous_queries"]) > 3:
            self.research_context["previous_queries"] = self.research_context["previous_queries"][-3:]
        
        return docs[:self.k]