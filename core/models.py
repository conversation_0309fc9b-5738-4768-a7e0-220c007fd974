from agno.models.azure import <PERSON>zureOpenAI
from agno.models.google import Gemini
from langchain_openai import AzureOpenAIEmbeddings
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from config.settings import AzureOpenAIConfig, GeminiConfig, AppConfig

class ModelFactory:
    """Factory for creating and configuring AI models."""
    
    @staticmethod
    def create_azure_openai_chat_model(config: AzureOpenAIConfig) -> AzureOpenAI:
        """Create Azure OpenAI chat model with backward compatibility."""
        model = AzureOpenAI(
            azure_deployment=config.chat_deployment,
            api_key=config.api_key,
            azure_endpoint=config.endpoint
        )
        
        # Alias chat methods to invoke for backward compatibility
        model.chat = model.invoke
        model.chat_stream = model.invoke_stream
        
        return model
    
    @staticmethod
    def create_azure_openai_embeddings(config: AzureOpenAIConfig) -> AzureOpenAIEmbeddings:
        """Create Azure OpenAI embeddings model."""
        return AzureOpenAIEmbeddings(
            azure_deployment=config.embedding_deployment,
            openai_api_key=config.api_key,
            azure_endpoint=config.endpoint
        )
    
    @staticmethod
    def create_gemini_chat_model(config: GeminiConfig) -> Gemini:
        """Create Google Gemini chat model."""
        if config.model_provider == "vertex_ai":
            model = Gemini(
                id=config.chat_model,
                vertexai=True,
                project_id=config.project_id,
                location=config.location
            )
        else:  # google_ai_studio
            model = Gemini(
                id=config.chat_model,
                api_key=config.api_key
            )
        
        # Alias methods for backward compatibility with your existing code
        model.chat = model.invoke
        model.chat_stream = model.invoke_stream
        
        return model
    
    @staticmethod
    def create_gemini_embeddings(config: GeminiConfig) -> GoogleGenerativeAIEmbeddings:
        """Create Google Gemini embeddings model."""
        if config.model_provider == "vertex_ai":
            return GoogleGenerativeAIEmbeddings(
                model=config.embedding_model,
                project=config.project_id,
                location=config.location,
                task_type="retrieval_document",
                dimensions=3072  # Match existing Qdrant collection
            )
        else:  # google_ai_studio
            return GoogleGenerativeAIEmbeddings(
                model=config.embedding_model,
                google_api_key=config.api_key,
                task_type="retrieval_document",
                dimensions=3072  # Match existing Qdrant collection
            )
    
    @staticmethod
    def create_chat_model(config: AppConfig):
        """Create chat model based on configured provider."""
        if config.model_provider == "gemini":
            return ModelFactory.create_gemini_chat_model(config.gemini)
        else:  # default to azure_openai
            return ModelFactory.create_azure_openai_chat_model(config.azure_openai)
    
    @staticmethod
    def create_embeddings_model(config: AppConfig):
        """Create embeddings model based on configured provider."""
        if config.model_provider == "gemini":
            return ModelFactory.create_gemini_embeddings(config.gemini)
        else:  # default to azure_openai
            return ModelFactory.create_azure_openai_embeddings(config.azure_openai)