import json
import urllib.request
import urllib.error
import asyncio
from typing import List, Tuple
from langchain.schema import Document
from config.settings import AzureMLConfig

class AzureMLReranker:
    """Azure ML reranking service using microsoft-deberta-large-mnli."""
    
    def __init__(self, config: AzureMLConfig):
        self.endpoint = config.endpoint
        self.api_key = config.api_key
    
    def _format_query_document_pairs(self, query: str, documents: List[Document]) -> List[str]:
        """Format query-document pairs for DeBERTa reranking model."""
        pairs = []
        for doc in documents:
            # Create hypothesis-premise pairs for NLI model
            # Format: "Query is relevant to: [document_content]"
            content = doc.page_content[:512]  # Limit content length for model
            pair_text = f"Query '{query}' is relevant to: {content}"
            pairs.append(pair_text)
        return pairs
    
    def _make_azure_ml_request(self, input_texts: List[str]) -> List[float]:
        """Make synchronous request to Azure ML endpoint."""
        data = {"input_data": input_texts}
        body = str.encode(json.dumps(data))
        
        headers = {
            'Content-Type': 'application/json', 
            'Accept': 'application/json', 
            'Authorization': f'Bearer {self.api_key}'
        }
        
        req = urllib.request.Request(self.endpoint, body, headers)
        
        try:
            response = urllib.request.urlopen(req, timeout=30)
            result = response.read()
            response_data = json.loads(result.decode('utf-8'))
            
            # Extract scores from response - handle various response formats
            if isinstance(response_data, list) and len(response_data) > 0:
                # Check if it's a list of dictionaries with label and score
                if isinstance(response_data[0], dict) and 'score' in response_data[0]:
                    scores = [float(item['score']) for item in response_data]
                # Direct list of numeric scores
                elif isinstance(response_data[0], (int, float)):
                    scores = [float(score) for score in response_data]
                else:
                    scores = [0.5] * len(input_texts)
            elif isinstance(response_data, dict):
                # Check various possible keys for scores
                if 'output_data' in response_data:
                    scores_data = response_data['output_data']
                    if isinstance(scores_data, list):
                        scores = [float(score) if isinstance(score, (int, float)) else 0.5 for score in scores_data]
                    else:
                        scores = [0.5] * len(input_texts)
                elif 'scores' in response_data:
                    scores = [float(score) for score in response_data['scores']]
                elif 'predictions' in response_data:
                    scores = [float(score) for score in response_data['predictions']]
                else:
                    # Try to extract any numeric values from the response
                    print(f"DEBUG: Unexpected response format, keys: {response_data.keys()}")
                    scores = [0.5] * len(input_texts)
            else:
                # Fallback: assume equal scores
                scores = [0.5] * len(input_texts)
            
            return scores
                
        except urllib.error.HTTPError as error:
            error_body = error.read().decode('utf8', 'ignore')
            # Re-raise the exception with more context
            raise ConnectionError(
                f"Azure ML API request failed with status {error.code}: {error_body}"
            ) from error
        except Exception as e:
            # Re-raise to allow the caller to handle it
            raise ConnectionError(f"An unexpected error occurred during Azure ML API request: {e}") from e
    
    async def rerank_batch(self, query: str, documents: List[Document]) -> List[Document]:
        """Rerank documents using Azure ML endpoint."""
        if not documents:
            return documents
        
        # Format query-document pairs for the model
        input_texts = self._format_query_document_pairs(query, documents)
        
        # Make async request using asyncio
        loop = asyncio.get_event_loop()
        scores = await loop.run_in_executor(None, self._make_azure_ml_request, input_texts)
        
        # Combine documents with their scores
        scored_docs: List[Tuple[float, Document]] = list(zip(scores, documents))
        
        # Sort by score in descending order
        scored_docs.sort(key=lambda x: x[0], reverse=True)
        
        # Return reranked documents
        return [doc for score, doc in scored_docs]