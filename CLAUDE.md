# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-attack cybersecurity threat intelligence agent with specializations for SQL injection, XSS, RCE, and other attack vectors. The system combines a static knowledge base (Qdrant vector database) with real-time web search capabilities to provide comprehensive, attack-specific security analysis.

## Development Commands

```bash
# Install dependencies (use system packages, no venv needed)
python3 -m pip install -r requirements.txt --break-system-packages

# Run the main agent with attack type selection
python3 sqli_agent.py --attack-type sqli    # SQL Injection specialization
python3 sqli_agent.py --attack-type xss     # Cross-Site Scripting specialization  
python3 sqli_agent.py --attack-type rce     # Remote Code Execution specialization
python3 sqli_agent.py --list-attacks        # List all available attack types

# Run the FastAPI streaming server
python3 main.py                              # Start API server on http://localhost:8000

# Test individual components
python3 tools/web_search.py                 # Test web search backends
python3 test_agent_tools.py                 # Test agent tools directly
python3 test_streaming_api.py               # Test streaming API functionality
python3 test_qdrant_connection.py           # Test Qdrant database connection
```

## Core Architecture

The application follows a modular factory pattern with clear separation of concerns:

- **Entry Points**: 
  - `sqli_agent.py` - Interactive CLI agent with attack type selection
  - `main.py` - FastAPI streaming server for real-time web interfaces
- **API Layer**: `api/` - FastAPI streaming endpoints with Server-Sent Events (SSE) support
- **Configuration Layer**: `config/` - Centralized settings management with environment variable validation
- **Agent Layer**: `agent/` - Agent factory, workflow management, and tool execution proxies using Agno framework
- **Core Components**: `core/` - Model factory, hybrid retriever, and reranking logic
- **Knowledge Management**: `knowledge/` - Knowledge base factory with LangChain-Qdrant integration
- **Tools**: `tools/` - Modular web search and knowledge search tools with async support

### Key Architecture Patterns

1. **Multi-Attack Profile System**: Attack-specific configurations using the Strategy Pattern with dynamic loading
2. **Factory Pattern**: All major components (Agent, Model, KnowledgeBase) use factories for consistent initialization
3. **Configuration Management**: Centralized config classes with environment validation (`config/settings.py`)
4. **Streaming Architecture**: Real-time SSE streaming with structured event types for web UI integration
5. **Tool Execution Proxy**: Interceptor pattern for reliable tool execution with proper error handling
6. **Async Tool Execution**: Built for parallel tool calls to optimize performance
7. **Hybrid Retrieval**: Combines dense and sparse search with LLM-based reranking

## Environment Setup

The application requires extensive environment configuration via `.env` file:

### Required Variables
- `ATTACK_PROFILE` - Attack specialization (sqli, xss, rce) - determines agent behavior and collection
- `QDRANT_URL` - Qdrant vector database endpoint
- `QDRANT_API_KEY` - Qdrant authentication key
- `COLLECTION_NAME` - Target Qdrant collection name (auto-assigned if empty based on attack profile)
- `AZURE_OPENAI_ENDPOINT` - Azure OpenAI service endpoint
- `AZURE_OPENAI_API_KEY` - Azure OpenAI API key
- `AZURE_OPENAI_CHAT_DEPLOYMENT` - Chat model deployment name
- `AZURE_OPENAI_EMBEDDING_DEPLOYMENT` - Embedding model deployment name

### Web Search Backend Configuration
- `WEB_SEARCH_BACKEND` - Backend choice: "bing", "google", or "serpapi" (default: "bing")
- `USE_JINA_READER` - Enable full content extraction: "true" or "false" (default: "false")

### Backend-Specific API Keys
- **Bing**: `AZURE_BING_SEARCH_ENDPOINT`, `AZURE_BING_SEARCH_KEY`
- **Google**: `GOOGLE_CSE_API_KEY`, `GOOGLE_CSE_CX`
- **SerpAPI**: `SERPAPI_KEY`
- **Jina Reader**: `JINA_API_KEY` (if USE_JINA_READER=true)

### Azure ML Reranking (Optional)
- `AZURE_ML_RERANK_ENDPOINT` - Azure ML endpoint for DeBERTa reranking model
- `AZURE_ML_RERANK_KEY` - Azure ML API key

### Google Gemini Support (Optional)
- `GOOGLE_API_KEY` - Google API key for Gemini models (alternative to Azure OpenAI)

## Streaming API

The system provides a real-time streaming API built with FastAPI and Server-Sent Events (SSE) for web interface integration.

### Quick Start
```bash
# Start the streaming server
python3 main.py

# API will be available at http://localhost:8000
# Interactive docs at http://localhost:8000/docs
```

### Key Endpoints
- `POST /api/v1/chat/stream` - Main streaming endpoint for agent interaction
- `GET /api/v1/chat/models` - List available attack types and configurations
- `GET /api/v1/chat/health` - Health check with configuration validation
- `GET /` - API information and available endpoints

### Streaming Events
The streaming API emits structured events optimized for UI display:
- `agent_start` - Agent initialization with configuration
- `llm_token` - Individual LLM tokens for real-time typing effect
- `tool_call_start` - Tool execution begins with parameters
- `tool_call_result` - Structured results with search results/knowledge documents
- `reasoning_step` - Agent thinking and intermediate steps
- `agent_complete` - Final completion with metrics
- `error` - Error handling with recovery suggestions

See `docs/streaming_api_usage.md` for complete integration examples and event schemas.

## Development Notes

### SSL/TLS Configuration
The application includes SSL workarounds for development environments (`tools/web_search.py:8-10`). The Google API backend specifically uses unverified SSL context to avoid version mismatch issues.

### Attack Profile System
The system implements a sophisticated multi-attack profile architecture:
- **Profile Definition**: Each attack type has a complete configuration in `config/profiles/` (e.g., `sqli_profile.py`)
- **Dynamic Configuration**: Agent behavior, prompts, and knowledge base collections are automatically configured based on the selected attack profile
- **Validation**: Attack profiles include comprehensive validation to ensure configuration completeness
- **Extensibility**: New attack types can be easily added by creating new profile files

### Current Reranking Implementation
The system currently uses simple LLM-based scoring for reranking. The `todo` file indicates plans to migrate to a proper reranking model using microsoft-deberta-large-mnli via Azure ML endpoint. Configuration for this is already in `config/settings.py:66-82`.

### Tool Call Architecture
- **KnowledgeSearchTool** (`tools/knowledge_search.py`): Async wrapper for knowledge base retrieval
- **WebSearchTool** (`tools/web_search.py`): Multi-backend web search with optional Jina Reader integration
- **Tool Execution Proxy** (`agent/tool_execution_proxy.py`): Reliable tool execution with error handling
- **Tool Interceptor** (`agent/tool_interceptor.py`): Intercepts and processes tool calls for streaming
- Both tools are designed for parallel execution to optimize performance

### Agent Workflow Structure
The agent follows a structured 4-phase research process (`agent/workflow.py`):
1. Foundational scan of knowledge base
2. Iterative deep dive with web verification
3. Gap analysis and discovery
4. Final synthesis and reporting

### Component Dependencies
- **Agno Framework**: Core agent orchestration and tool management
- **FastAPI**: Web framework for streaming API endpoints with CORS support
- **LangChain**: Document processing and Qdrant integration
- **Qdrant Client**: Vector database operations
- **Azure OpenAI / Google Gemini**: LLM and embedding services
- **Multi-backend Search**: Supports Bing, Google Custom Search, and SerpAPI
- **SSE-Starlette**: Server-Sent Events streaming for real-time UI updates

## Important File Locations

- **Configuration**: Use `.env.example` as template for environment setup
- **API Documentation**: `docs/streaming_api_usage.md` contains complete streaming API examples
- **Test Scripts**: Multiple test files available for component validation
- **Attack Profiles**: Add new attack types in `config/profiles/` following existing patterns

