#!/usr/bin/env python3
"""
Enhanced test script for the streaming API implementation.
Includes server management, comprehensive debugging, and tool event analysis.
"""

import asyncio
import json
import aiohttp
import subprocess
import signal
import time
import threading
import queue
import sys
from typing import AsyncGenerator, Optional

class StreamingTestSuite:
    """Comprehensive test suite with server management and debugging."""
    
    def __init__(self, port: int = 8000):
        self.port = port
        self.server_process: Optional[subprocess.Popen] = None
        self.server_logs = queue.Queue()
        self.log_thread: Optional[threading.Thread] = None
        self.debug_mode = True
        
    def start_server(self):
        """Start the FastAPI server and capture logs."""
        print("🚀 Starting FastAPI server...")
        
        # Start server process
        self.server_process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Start log capture thread
        self.log_thread = threading.Thread(target=self._capture_logs, daemon=True)
        self.log_thread.start()
        
        print(f"📋 Server process started (PID: {self.server_process.pid})")
        
    def _capture_logs(self):
        """Capture server logs in a separate thread."""
        if not self.server_process:
            return
            
        for line in iter(self.server_process.stdout.readline, ''):
            if line:
                timestamp = time.strftime("%H:%M:%S")
                log_entry = f"[{timestamp}] {line.strip()}"
                self.server_logs.put(log_entry)
                if self.debug_mode:
                    print(f"🖥️  {log_entry}")
    
    async def wait_for_server_ready(self, timeout: int = 30):
        """Wait for server to be ready with health check polling."""
        print(f"⏳ Waiting for server to be ready (timeout: {timeout}s)...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"http://localhost:{self.port}/health") as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f"✅ Server ready! Status: {data.get('status')}")
                            return True
            except:
                pass
            
            await asyncio.sleep(1)
        
        print("❌ Server failed to start within timeout")
        return False
    
    def get_recent_logs(self, count: int = 10) -> list:
        """Get recent server logs."""
        logs = []
        temp_queue = queue.Queue()
        
        # Extract logs without losing them
        while not self.server_logs.empty() and len(logs) < count:
            log = self.server_logs.get()
            logs.append(log)
            temp_queue.put(log)
        
        # Put logs back
        while not temp_queue.empty():
            self.server_logs.put(temp_queue.get())
            
        return logs[-count:]  # Return most recent
    
    def cleanup(self):
        """Clean shutdown of server and resources."""
        print("\n🧹 Cleaning up...")
        
        if self.server_process:
            print(f"🛑 Stopping server (PID: {self.server_process.pid})")
            self.server_process.terminate()
            
            # Wait for graceful shutdown
            try:
                self.server_process.wait(timeout=10)
                print("✅ Server stopped gracefully")
            except subprocess.TimeoutExpired:
                print("⚠️  Force killing server")
                self.server_process.kill()
                self.server_process.wait()
        
        # Show final logs
        recent_logs = self.get_recent_logs(5)
        if recent_logs:
            print("\n📋 Final server logs:")
            for log in recent_logs:
                print(f"   {log}")

async def test_streaming_endpoint_with_debug(test_suite: StreamingTestSuite):
    """Test the streaming chat endpoint with comprehensive debugging."""
    
    url = f"http://localhost:{test_suite.port}/api/v1/chat/stream"
    data = {
        "query": "What are the latest SQL injection attack vectors and how do they work?",
        "attack_type": "sqli"
    }
    
    print("🚀 Testing Enhanced Streaming API Endpoint...")
    print(f"URL: {url}")
    print(f"Query: {data['query']}")
    print(f"Attack Type: {data['attack_type']}")
    print("-" * 80)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    print(f"❌ Error: HTTP {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
                    
                    # Show recent server logs for debugging
                    print("\n📋 Recent server logs:")
                    for log in test_suite.get_recent_logs(10):
                        print(f"   {log}")
                    return
                
                print("✅ Connection established")
                print("📡 Streaming events with debug analysis:")
                print("-" * 80)
                
                event_count = 0
                current_event_type = 'unknown'
                tool_events = []
                reasoning_events = []
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('event: '):
                        current_event_type = line[7:]  # Remove 'event: ' prefix
                        print(f"\n📨 Event Type: {current_event_type}")
                        
                    elif line.startswith('data: '):
                        try:
                            event_data = json.loads(line[6:])
                            event_count += 1
                            
                            print(f"Event #{event_count} ({current_event_type}):")
                            
                            # Debug: Show raw event data structure
                            if test_suite.debug_mode:
                                print(f"🔍 Raw Event Data Keys: {list(event_data.keys())}")
                            
                            # The data is the actual content  
                            data_content = event_data
                            
                            if current_event_type == 'agent_start':
                                print(f"  Agent: {data_content.get('display_name')}")
                                print(f"  Attack Type: {data_content.get('attack_type')}")
                                print(f"  Knowledge Base: {data_content.get('knowledge_base')}")
                                
                            elif current_event_type == 'initialization':
                                message = data_content.get('message', '')
                                print(f"  Status: {message}")
                                
                            elif current_event_type == 'agent_ready':
                                message = data_content.get('message', '')
                                print(f"  Status: {message}")
                                
                            elif current_event_type == 'llm_token':
                                token = data_content.get('token', '')
                                print(f"  Token: '{token[:50]}{'...' if len(token) > 50 else ''}'")
                                
                            elif current_event_type == 'tool_call_start':
                                # Enhanced tool debugging
                                tool_name = data_content.get('tool_name')
                                tool_input = data_content.get('tool_input', {})
                                
                                print(f"  🔧 Tool: {tool_name}")
                                print(f"  📝 Input Keys: {list(tool_input.keys())}")
                                
                                # Detailed tool input analysis
                                if test_suite.debug_mode:
                                    print(f"  🔍 Full Tool Input: {json.dumps(tool_input, indent=4)}")
                                
                                # Look for search queries
                                query_key = None
                                for key in ['query', 'search_query', 'search_term', 'q']:
                                    if key in tool_input:
                                        query_key = key
                                        break
                                
                                if query_key:
                                    print(f"  🔍 Search Query: '{tool_input[query_key]}'")
                                else:
                                    print(f"  ⚠️  No search query found in tool input")
                                
                                tool_events.append({
                                    'name': tool_name,
                                    'input': tool_input,
                                    'event_num': event_count
                                })
                                
                            elif current_event_type == 'tool_call_result':
                                tool_name = data_content.get('tool_name')
                                success = data_content.get('success')
                                result_summary = data_content.get('result_summary', '')
                                
                                print(f"  🔧 Tool: {tool_name}")
                                print(f"  ✅ Success: {success}")
                                
                                if result_summary:
                                    print(f"  📊 {result_summary}")
                                
                                if data_content.get('search_results'):
                                    results = data_content['search_results']
                                    print(f"  🌐 Web Search Results: {len(results)} found")
                                    for i, result in enumerate(results[:2]):  # Show first 2
                                        print(f"    {i+1}. {result.get('title', 'No title')}")
                                        print(f"       URL: {result.get('url', 'No URL')}")
                                
                                if data_content.get('knowledge_documents'):
                                    docs = data_content['knowledge_documents']
                                    print(f"  📚 Knowledge Documents: {len(docs)} found")
                                    for i, doc in enumerate(docs[:2]):  # Show first 2
                                        print(f"    {i+1}. {doc.get('source', 'Unknown source')}")
                                        preview = doc.get('content_preview', '')
                                        print(f"       Preview: {preview[:50]}...")
                                
                            elif current_event_type == 'reasoning_step':
                                message = data_content.get('message', '')
                                step_type = data_content.get('step_type', 'thinking')
                                detailed_reasoning = data_content.get('detailed_reasoning', '')
                                
                                print(f"  🧠 Step Type: {step_type}")
                                print(f"  💭 Message: {message}")
                                
                                if detailed_reasoning:
                                    print(f"  📖 Detailed Reasoning: {detailed_reasoning[:100]}...")
                                
                                reasoning_events.append({
                                    'type': step_type,
                                    'message': message,
                                    'detailed': detailed_reasoning,
                                    'event_num': event_count
                                })
                                
                            elif current_event_type == 'agent_complete':
                                final_answer = data_content.get('final_answer', '')
                                total_time = data_content.get('total_time_ms', 0)
                                tools_used = data_content.get('tools_used', [])
                                
                                print(f"  📏 Final Answer Length: {len(final_answer)} chars")
                                print(f"  ⏱️  Total Time: {total_time:.1f}ms")
                                print(f"  🔧 Tools Used: {', '.join(tools_used) if tools_used else 'None'}")
                                
                                # Critical test: Check if final answer is properly captured
                                if final_answer and final_answer.strip():
                                    print(f"  ✅ FINAL ANSWER CAPTURED SUCCESSFULLY!")
                                    print(f"  📝 Preview: {final_answer[:150]}...")
                                else:
                                    print(f"  ❌ FINAL ANSWER IS MISSING OR EMPTY!")
                                
                                # Exit the loop after agent_complete
                                break
                                
                            elif current_event_type == 'error':
                                error_msg = data_content.get('message')
                                error_type = data_content.get('error_type')
                                print(f"  ❌ Error: {error_type}")
                                print(f"  📝 Message: {error_msg}")
                                
                                # Show recent server logs for error context
                                print("\n📋 Recent server logs for error context:")
                                for log in test_suite.get_recent_logs(5):
                                    print(f"     {log}")
                                break
                            
                        except json.JSONDecodeError as e:
                            print(f"⚠️  JSON decode error: {e}")
                            print(f"   Raw line: {line}")
                
                # Summary analysis
                print(f"\n🎉 Stream completed! Total events: {event_count}")
                print(f"🔧 Tool calls detected: {len(tool_events)}")
                print(f"🧠 Reasoning steps: {len(reasoning_events)}")
                
                # Tool analysis summary
                if tool_events:
                    print(f"\n🔍 Tool Event Analysis:")
                    for i, tool in enumerate(tool_events):
                        print(f"   {i+1}. {tool['name']} (Event #{tool['event_num']})")
                        if tool['input']:
                            # Look for query-like keys
                            query_keys = [k for k in tool['input'].keys() if 'query' in k.lower() or k in ['q', 'search_term']]
                            if query_keys:
                                for qk in query_keys:
                                    print(f"      Query: {tool['input'][qk]}")
                
                # Reasoning analysis summary
                if reasoning_events:
                    print(f"\n🧠 Reasoning Event Analysis:")
                    reasoning_types = {}
                    for event in reasoning_events:
                        reasoning_types[event['type']] = reasoning_types.get(event['type'], 0) + 1
                    
                    for rtype, count in reasoning_types.items():
                        print(f"   {rtype}: {count} events")
                
    except aiohttp.ClientError as e:
        print(f"❌ Connection error: {e}")
        print("\n📋 Recent server logs:")
        for log in test_suite.get_recent_logs(10):
            print(f"   {log}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("\n📋 Recent server logs:")
        for log in test_suite.get_recent_logs(10):
            print(f"   {log}")

async def test_streaming_endpoint():
    """Basic streaming test (simplified version)."""
    
    url = "http://localhost:8000/api/v1/chat/stream"
    data = {
        "query": "What are the latest SQL injection attack vectors and how do they work?",
        "attack_type": "sqli"
    }
    
    print("🚀 Testing Basic Streaming API Endpoint...")
    print(f"URL: {url}")
    print(f"Query: {data['query']}")
    print(f"Attack Type: {data['attack_type']}")
    print("-" * 60)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    print(f"❌ Error: HTTP {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
                    return
                
                print("✅ Connection established")
                print("📡 Streaming events:")
                print("-" * 60)
                
                event_count = 0
                current_event_type = 'unknown'
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('event: '):
                        current_event_type = line[7:]  # Remove 'event: ' prefix
                        print(f"📨 Event Type: {current_event_type}")
                        
                    elif line.startswith('data: '):
                        try:
                            event_data = json.loads(line[6:])
                            event_count += 1
                            print(f"Event #{event_count} ({current_event_type}): {list(event_data.keys())}")
                            
                            if current_event_type == 'agent_complete':
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"⚠️  JSON decode error: {e}")
                            print(f"   Raw line: {line}")
                
                print(f"🎉 Stream completed! Total events: {event_count}")
                
    except aiohttp.ClientError as e:
        print(f"❌ Connection error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

async def test_health_endpoint():
    """Test the health check endpoint."""
    
    url = "http://localhost:8000/api/v1/chat/health"
    
    print("🏥 Testing Health Endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Health check passed")
                    print(f"   Status: {data.get('status')}")
                    print(f"   Message: {data.get('message')}")
                else:
                    print(f"❌ Health check failed: HTTP {response.status}")
                    
    except Exception as e:
        print(f"❌ Health check error: {e}")

async def test_models_endpoint():
    """Test the models listing endpoint."""
    
    url = "http://localhost:8000/api/v1/chat/models"
    
    print("📋 Testing Models Endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Models endpoint working")
                    models = data.get('available_models', [])
                    print(f"   Available Models: {len(models)}")
                    for model in models:
                        print(f"   - {model.get('attack_type')}: {model.get('display_name')}")
                else:
                    print(f"❌ Models endpoint failed: HTTP {response.status}")
                    
    except Exception as e:
        print(f"❌ Models endpoint error: {e}")

async def main():
    """Run comprehensive tests with server management."""
    
    test_suite = StreamingTestSuite(port=8000)
    
    try:
        print("🧪 Starting Enhanced Streaming API Tests with Server Management")
        print("=" * 80)
        
        # Start the server
        test_suite.start_server()
        
        # Wait for server to be ready
        if not await test_suite.wait_for_server_ready():
            print("❌ Server failed to start, aborting tests")
            return
        
        print("=" * 80)
        print("🧪 Running API Tests...")
        print("=" * 80)
        
        # Test basic endpoints first
        await test_health_endpoint()
        print()
        
        await test_models_endpoint() 
        print()
        
        # Test streaming endpoint with full debugging
        await test_streaming_endpoint_with_debug(test_suite)
        
        print("=" * 80)
        print("🏁 All tests completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"❌ Test suite error: {e}")
        
        # Show recent server logs for debugging
        print("\n📋 Recent server logs for error context:")
        for log in test_suite.get_recent_logs(15):
            print(f"   {log}")
    finally:
        # Always cleanup
        test_suite.cleanup()

if __name__ == "__main__":
    try:
        # Setup signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            print("\n🛑 Received interrupt signal, cleaning up...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)