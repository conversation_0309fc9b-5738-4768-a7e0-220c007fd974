<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cybersecurity Agent Streaming Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .chat-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            height: 70vh;
        }
        
        .main-chat {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .input-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .query-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .attack-select {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .send-btn:hover {
            background: #0056b3;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background: #fafafa;
        }
        
        .event {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .event.agent-start {
            background: #e7f3ff;
            border-left-color: #007bff;
        }
        
        .event.llm-token {
            background: #f0f8ff;
            border-left-color: #17a2b8;
        }
        
        .event.tool-call-start {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .event.tool-call-result {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .event.reasoning-step {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .event.thinking-step {
            background: #e1f5fe;
            border-left-color: #01579b;
        }
        
        .event.analysis-step {
            background: #f3e5f5;
            border-left-color: #4a148c;
        }
        
        .event.agent-complete {
            background: #d1ecf1;
            border-left-color: #138496;
        }
        
        .event.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .event.initialization {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .event.agent-ready {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .event.knowledge-search {
            background: #e1f5fe;
            border-left-color: #0288d1;
        }
        
        .event.web-search {
            background: #fff8e1;
            border-left-color: #ffa000;
        }
        
        .search-query {
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .result-summary {
            font-weight: bold;
            color: #2196f3;
        }
        
        .reasoning-message {
            margin-left: 10px;
            font-style: italic;
        }
        
        .badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-left: 5px;
        }
        
        .score {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 3px;
        }
        
        .event-type {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .search-result, .knowledge-doc {
            margin: 5px 0;
            padding: 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
        
        .search-result h4, .knowledge-doc h4 {
            margin: 0 0 5px 0;
            color: #007bff;
            font-size: 14px;
        }
        
        .search-result .url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }
        
        .snippet, .preview {
            font-size: 13px;
            color: #555;
            margin-top: 5px;
        }
        
        .stats {
            margin-top: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status.idle {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status.streaming {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.complete {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .response-text {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Cybersecurity Threat Intelligence Agent</h1>
        <p>Real-time streaming interface showing every LLM call, tool execution, and agent thinking</p>
    </div>
    
    <div class="chat-container">
        <div class="main-chat">
            <div class="input-section">
                <input type="text" class="query-input" placeholder="Ask about cybersecurity threats..." 
                       value="What are the latest SQL injection attack vectors and how can I detect them?">
                <select class="attack-select">
                    <option value="sqli">SQL Injection</option>
                    <option value="xss">Cross-Site Scripting</option>
                    <option value="rce">Remote Code Execution</option>
                </select>
                <button class="send-btn" onclick="startStreaming()">Send</button>
                <button class="send-btn" onclick="warmupAgents()" style="background: #28a745;">Warmup</button>
            </div>
            
            <div class="messages" id="messages"></div>
            
            <div class="response-text" id="responseText"></div>
        </div>
        
        <div class="sidebar">
            <div class="status idle" id="status">Ready</div>
            
            <div class="stats">
                <div class="stat-item">
                    <span>Events Received:</span>
                    <span id="eventCount">0</span>
                </div>
                <div class="stat-item">
                    <span>Tokens Streamed:</span>
                    <span id="tokenCount">0</span>
                </div>
                <div class="stat-item">
                    <span>Tools Used:</span>
                    <span id="toolsUsed">None</span>
                </div>
                <div class="stat-item">
                    <span>Duration:</span>
                    <span id="duration">0s</span>
                </div>
                <div class="stat-item">
                    <span>Think & Analyze:</span>
                    <span id="reasoningCount">0</span>
                </div>
            </div>
            
            <h3>💭 Agent Think & Analyze</h3>
            <div id="reasoningPanel" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; border-radius: 5px; padding: 10px; margin-bottom: 15px; font-size: 12px;">
                <div style="text-align: center; color: #6c757d; font-style: italic;">
                    Agent thinking and analysis will appear here...
                </div>
            </div>
            
            <h3>Recent Events</h3>
            <div id="eventLog"></div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:8000';
        
        let eventSource = null;
        let eventCount = 0;
        let tokenCount = 0;
        let toolsUsed = new Set();
        let reasoningCount = 0;
        let startTime = null;
        let durationInterval = null;
        
        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }
        
        function updateStats() {
            document.getElementById('eventCount').textContent = eventCount;
            document.getElementById('tokenCount').textContent = tokenCount;
            document.getElementById('toolsUsed').textContent = toolsUsed.size > 0 ? Array.from(toolsUsed).join(', ') : 'None';
            document.getElementById('reasoningCount').textContent = reasoningCount;
        }
        
        function addToReasoningPanel(message, type = 'reasoning') {
            const reasoningPanel = document.getElementById('reasoningPanel');
            
            // Clear the initial message if it's the first reasoning step
            if (reasoningCount === 0) {
                reasoningPanel.innerHTML = '';
            }
            
            const reasoningEntry = document.createElement('div');
            reasoningEntry.style.cssText = 'margin-bottom: 8px; padding: 6px; border-left: 3px solid #007bff; background: white; border-radius: 3px;';
            
            const icon = {
                'thinking': '💭',
                'analysis': '📊',
                'reasoning': '🧠'
            }[type] || '🧠';
            
            const timestamp = new Date().toLocaleTimeString();
            reasoningEntry.innerHTML = `
                <div style="font-weight: bold; color: #007bff; margin-bottom: 2px;">
                    ${icon} ${type.toUpperCase()} - ${timestamp}
                </div>
                <div style="color: #333; line-height: 1.3;">
                    ${message.replace(/^(💭|📊|🧠)\s*/, '')}
                </div>
            `;
            
            reasoningPanel.appendChild(reasoningEntry);
            reasoningPanel.scrollTop = reasoningPanel.scrollHeight;
            
            reasoningCount++;
        }
        
        function startDurationTimer() {
            startTime = Date.now();
            durationInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                document.getElementById('duration').textContent = `${elapsed}s`;
            }, 1000);
        }
        
        function stopDurationTimer() {
            if (durationInterval) {
                clearInterval(durationInterval);
                durationInterval = null;
            }
        }
        
        function addEvent(type, data) {
            eventCount++;
            
            const messagesEl = document.getElementById('messages');
            const eventEl = document.createElement('div');
            eventEl.className = `event ${type.replace('_', '-')}`;
            
            let content = `<div class="event-type">${type.toUpperCase()}</div>`;
            
            switch(type) {
                case 'initialization':
                    content += `<div><strong>Status:</strong> ${data.message}</div>`;
                    break;
                    
                case 'agent_ready':
                    content += `<div><strong>Status:</strong> ${data.message}</div>`;
                    break;
                    
                case 'agent_start':
                    content += `
                        <div><strong>Agent:</strong> ${data.display_name}</div>
                        <div><strong>Attack Type:</strong> ${data.attack_type}</div>
                        <div><strong>Knowledge Base:</strong> ${data.knowledge_base}</div>
                        <div><strong>Specialized Terms:</strong> ${data.specialized_terms?.join(', ')}</div>
                    `;
                    break;
                    
                case 'llm_token':
                    tokenCount++;
                    const responseEl = document.getElementById('responseText');
                    responseEl.textContent += data.token;
                    responseEl.scrollTop = responseEl.scrollHeight;
                    content += `<div>Token: "${data.token}"</div>`;
                    break;
                    
                case 'tool_call_start':
                    toolsUsed.add(data.tool_name);
                    content += `
                        <div><strong>Tool:</strong> ${data.tool_name}</div>
                        <div><strong>Input:</strong> ${JSON.stringify(data.tool_input)}</div>
                    `;
                    break;
                    
                case 'tool_call_result':
                    content += `<div><strong>Tool:</strong> ${data.tool_name}</div>`;
                    content += `<div><strong>Success:</strong> ${data.success}</div>`;
                    
                    // Show result summary if available
                    if (data.result_summary) {
                        content += `<div class="result-summary">${data.result_summary}</div>`;
                    }
                    
                    if (data.search_results) {
                        content += `<div><strong>Web Search Results (${data.search_results.length}):</strong></div>`;
                        data.search_results.forEach((result, i) => {
                            content += `
                                <div class="search-result">
                                    <h4>${result.title}</h4>
                                    <div class="url">${result.url}</div>
                                    <div class="snippet">${result.snippet}</div>
                                    ${result.has_full_content ? '<span class="badge">Full Content</span>' : ''}
                                </div>
                            `;
                        });
                    }
                    
                    if (data.knowledge_documents) {
                        content += `<div><strong>Knowledge Base Documents (${data.knowledge_documents.length}):</strong></div>`;
                        data.knowledge_documents.forEach((doc, i) => {
                            content += `
                                <div class="knowledge-doc">
                                    <h4>${doc.source}</h4>
                                    <div class="preview">${doc.content_preview}</div>
                                    ${doc.relevance_score ? `<div class="score">Relevance: ${(doc.relevance_score * 100).toFixed(1)}%</div>` : ''}
                                </div>
                            `;
                        });
                    }
                    break;
                    
                case 'reasoning_step':
                    // Simplified reasoning step display - only think/analyze tools
                    const stepIcon = {
                        'knowledge_search': '🔍',
                        'web_search': '🌐',
                        'think_tool': '💭',
                        'analyze_tool': '📊'
                    }[data.step_type] || '⚡';
                    
                    // Only process think and analyze tool events
                    if (data.step_type && ['think_tool', 'analyze_tool'].includes(data.step_type)) {
                        content += `
                            <div><strong>${stepIcon} ${data.step_type.replace('_', ' ').toUpperCase()}:</strong></div>
                            <div class="reasoning-message">${data.message}</div>
                        `;
                        
                        // Show detailed reasoning if available
                        if (data.detailed_reasoning) {
                            content += `
                                <details style="margin-top: 8px;">
                                    <summary><strong>Detailed Reasoning</strong></summary>
                                    <div style="margin-top: 5px; font-style: italic;">${data.detailed_reasoning}</div>
                                </details>
                            `;
                        }
                        
                        // Add to reasoning panel
                        const reasoningType = data.step_type.includes('think') ? 'thinking' : 'analysis';
                        addToReasoningPanel(data.message, reasoningType);
                    } else if (data.step_type && ['knowledge_search', 'web_search'].includes(data.step_type)) {
                        // Handle search query events (non-reasoning)
                        content += `
                            <div><strong>${stepIcon} ${data.step_type.replace('_', ' ').toUpperCase()}:</strong></div>
                            <div class="reasoning-message">${data.message}</div>
                        `;
                    }
                    break;
                    
                case 'thinking_step':
                    // Agent thinking step display
                    content += `
                        <div><strong>💭 AGENT THINKING:</strong></div>
                        <div class="reasoning-message" style="background: rgba(1, 87, 155, 0.1); padding: 8px; border-radius: 4px; margin-top: 5px;">
                            ${data.message}
                        </div>
                    `;
                    if (data.tool_data && Object.keys(data.tool_data).length > 0) {
                        content += `
                            <details style="margin-top: 5px;">
                                <summary><em>View thinking details</em></summary>
                                <pre style="font-size: 11px; margin-top: 3px;">${JSON.stringify(data.tool_data, null, 2)}</pre>
                            </details>
                        `;
                    }
                    // Add to reasoning panel
                    addToReasoningPanel(data.message, 'thinking');
                    break;
                    
                case 'analysis_step':
                    // Agent analysis step display
                    content += `
                        <div><strong>📊 AGENT ANALYSIS:</strong></div>
                        <div class="reasoning-message" style="background: rgba(74, 20, 140, 0.1); padding: 8px; border-radius: 4px; margin-top: 5px;">
                            ${data.message}
                        </div>
                    `;
                    if (data.tool_data && Object.keys(data.tool_data).length > 0) {
                        content += `
                            <details style="margin-top: 5px;">
                                <summary><em>View analysis details</em></summary>
                                <pre style="font-size: 11px; margin-top: 3px;">${JSON.stringify(data.tool_data, null, 2)}</pre>
                            </details>
                        `;
                    }
                    // Add to reasoning panel
                    addToReasoningPanel(data.message, 'analysis');
                    break;
                    
                case 'agent_complete':
                    content += `
                        <div><strong>Response Length:</strong> ${data.final_answer?.length || 0} characters</div>
                        <div><strong>Total Time:</strong> ${data.total_time_ms?.toFixed(1) || 0}ms</div>
                        <div><strong>Tools Used:</strong> ${data.tools_used?.join(', ') || 'None'}</div>
                    `;
                    break;
                    
                case 'error':
                    content += `
                        <div style="color: red;"><strong>Error:</strong> ${data.message}</div>
                        <div><strong>Type:</strong> ${data.error_type}</div>
                    `;
                    break;
            }
            
            eventEl.innerHTML = content;
            messagesEl.appendChild(eventEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
            
            // Add to event log
            const eventLog = document.getElementById('eventLog');
            const logEntry = document.createElement('div');
            logEntry.style.cssText = 'font-size: 12px; margin-bottom: 5px; padding: 5px; background: #f8f9fa; border-radius: 3px;';
            logEntry.innerHTML = `<strong>${type}</strong><br>${new Date().toLocaleTimeString()}`;
            eventLog.insertBefore(logEntry, eventLog.firstChild);
            
            // Keep only last 10 log entries
            while (eventLog.children.length > 10) {
                eventLog.removeChild(eventLog.lastChild);
            }
            
            updateStats();
        }
        
        function startStreaming() {
            const query = document.querySelector('.query-input').value;
            const attackType = document.querySelector('.attack-select').value;
            
            if (!query.trim()) {
                alert('Please enter a query');
                return;
            }
            
            // Reset state
            document.getElementById('messages').innerHTML = '';
            document.getElementById('responseText').textContent = '';
            document.getElementById('eventLog').innerHTML = '';
            document.getElementById('reasoningPanel').innerHTML = '<div style="text-align: center; color: #6c757d; font-style: italic;">Agent thinking and analysis will appear here...</div>';
            eventCount = 0;
            tokenCount = 0;
            toolsUsed.clear();
            reasoningCount = 0;
            updateStats();
            
            // Close existing connection
            if (eventSource) {
                eventSource.close();
            }
            
            updateStatus('streaming', 'Connecting to agent...');
            startDurationTimer();
            
            // Start streaming
            fetch(`${API_BASE_URL}/api/v1/chat/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    attack_type: attackType
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('complete', 'Stream completed');
                            stopDurationTimer();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');
                        
                        let currentEvent = null;
                        let currentData = null;
                        
                        for (const line of lines) {
                            if (line.startsWith('event: ')) {
                                currentEvent = line.slice(7).trim();
                            } else if (line.startsWith('data: ')) {
                                try {
                                    currentData = JSON.parse(line.slice(6));
                                } catch (e) {
                                    console.error('JSON parse error:', e, line);
                                    continue;
                                }
                            } else if (line.trim() === '' && currentEvent && currentData) {
                                // Empty line indicates end of SSE message
                                addEvent(currentEvent, currentData);
                                currentEvent = null;
                                currentData = null;
                            }
                        }
                        
                        // Handle case where we have pending event data at end of chunk
                        if (currentEvent && currentData) {
                            addEvent(currentEvent, currentData);
                        }
                        
                        return readStream();
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('Streaming error:', error);
                updateStatus('error', `Error: ${error.message}`);
                stopDurationTimer();
            });
        }
        
        function warmupAgents() {
            updateStatus('streaming', 'Warming up agents...');
            
            fetch(`${API_BASE_URL}/api/v1/chat/warmup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                updateStatus('complete', `Warmup complete: ${data.total_ready}/${data.total_agents} agents ready`);
                
                // Add warmup results to messages
                const messagesEl = document.getElementById('messages');
                const eventEl = document.createElement('div');
                eventEl.className = 'event agent-ready';
                eventEl.innerHTML = `
                    <div class="event-type">WARMUP COMPLETE</div>
                    <div><strong>Ready:</strong> ${data.total_ready}/${data.total_agents} agents</div>
                    <div><strong>Results:</strong></div>
                    ${Object.entries(data.results).map(([type, result]) => 
                        `<div style="margin-left: 10px;">
                            ${type}: ${result.status} 
                            ${result.initialization_time_ms ? `(${result.initialization_time_ms.toFixed(1)}ms)` : ''}
                        </div>`
                    ).join('')}
                `;
                messagesEl.appendChild(eventEl);
                messagesEl.scrollTop = messagesEl.scrollHeight;
            })
            .catch(error => {
                updateStatus('error', `Warmup failed: ${error.message}`);
            });
        }
        
        // Allow Enter key to send
        document.querySelector('.query-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                startStreaming();
            }
        });
        
        // Initialize
        updateStats();
    </script>
</body>
</html>