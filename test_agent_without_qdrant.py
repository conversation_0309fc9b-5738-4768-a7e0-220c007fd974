#!/usr/bin/env python3
"""
Test agent creation without Qdrant connection.
"""

import asyncio
from config.settings import AppConfig
from core.models import ModelFactory
from agent.agent_factory import AgentFactory

async def test_agent_creation():
    """Test creating agent without knowledge base."""
    
    print("🤖 Testing Agent Creation Without Qdrant")
    print("=" * 50)
    
    try:
        # Create configuration
        print("📋 Loading configuration...")
        config = AppConfig.from_env("sqli")
        attack_profile = config.get_attack_profile()
        print(f"✅ Configuration loaded for {attack_profile.display_name}")
        
        # Create chat model
        print("🧠 Creating chat model...")
        chat_model = ModelFactory.create_chat_model(config)
        print("✅ Chat model created successfully")
        
        # Create agent without knowledge base (None retriever)
        print("🔧 Creating agent without knowledge base...")
        agent = AgentFactory.create_agent(
            chat_model, None, config.web_search, attack_profile
        )
        print("✅ Agent created successfully in fallback mode")
        
        # Test a simple query
        print("💬 Testing agent response...")
        response = await agent.arun("What is SQL injection?")
        print("✅ Agent responded successfully!")
        print(f"Response preview: {response[:200]}...")
        
        print("\n🎉 Agent test completed successfully!")
        print("Agent can work without Qdrant using web search only.")
        
    except Exception as e:
        print(f"❌ Agent test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent_creation())