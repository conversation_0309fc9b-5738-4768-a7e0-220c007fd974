#!/usr/bin/env python3
"""
Test script to trigger 5 concurrent knowledge retrieval calls.
"""

import requests
import json
import time

def test_concurrent_knowledge():
    """Test that specifically triggers multiple concurrent knowledge searches."""
    
    url = "http://localhost:8000/api/v1/chat/stream"
    
    # This query should trigger multiple knowledge base searches
    query = "Make 5 concurrent random knowledge retrievals about: SQL injection payloads, blind SQL injection, time-based SQLi, error-based SQLi, and union-based SQLi, and I want exactly 1 document for each"
    
    payload = {
        "query": query,
        "attack_type": "sqli",
        "num_documents": 1
    }
    
    print(f"🧪 Testing Concurrent Knowledge Retrievals")
    print(f"URL: {url}")
    print(f"Query: {query}")
    print("-" * 80)
    
    try:
        response = requests.post(url, json=payload, stream=True, timeout=60)
        response.raise_for_status()
        
        print("✅ Connection established")
        print("📡 Monitoring knowledge retrieval events:")
        print("-" * 80)
        
        event_count = 0
        knowledge_calls = []
        knowledge_results = []
        
        for line in response.iter_lines(decode_unicode=True):
            if line.strip():
                if line.startswith('event: '):
                    current_event = line[7:].strip()
                elif line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        event_count += 1
                        
                        if current_event == 'tool_call_start' and data.get('tool_name') == 'search_knowledge_base':
                            knowledge_calls.append({
                                'call_id': data.get('call_id'),
                                'query': data.get('tool_input', {}).get('query'),
                                'event_num': event_count
                            })
                            print(f"🔍 Knowledge Call #{len(knowledge_calls)} (Event #{event_count})")
                            print(f"   Call ID: {data.get('call_id')}")
                            print(f"   Query: {data.get('tool_input', {}).get('query')}")
                        
                        elif current_event == 'tool_call_result' and data.get('tool_name') == 'search_knowledge_base':
                            knowledge_results.append({
                                'call_id': data.get('call_id'),
                                'success': data.get('success'),
                                'result_summary': data.get('result_summary'),
                                'event_num': event_count
                            })
                            success_icon = "✅" if data.get('success') else "❌"
                            print(f"{success_icon} Knowledge Result #{len(knowledge_results)} (Event #{event_count})")
                            print(f"   Call ID: {data.get('call_id')}")
                            print(f"   Success: {data.get('success')}")
                            print(f"   Summary: {data.get('result_summary', 'No summary')}")
                        
                        elif current_event == 'agent_complete':
                            print(f"\n🎉 Stream completed! Total events: {event_count}")
                            break
                            
                    except json.JSONDecodeError:
                        continue
        
        print("\n" + "=" * 80)
        print("📊 ANALYSIS")
        print("=" * 80)
        print(f"Knowledge calls made: {len(knowledge_calls)}")
        print(f"Knowledge results received: {len(knowledge_results)}")
        
        successful_results = sum(1 for r in knowledge_results if r['success'])
        failed_results = len(knowledge_results) - successful_results
        
        print(f"Successful: {successful_results}")
        print(f"Failed: {failed_results}")
        
        if len(knowledge_calls) >= 5:
            print("✅ Successfully triggered 5+ concurrent knowledge calls")
        else:
            print(f"⚠️ Only triggered {len(knowledge_calls)} knowledge calls (expected 5+)")
        
        if successful_results == len(knowledge_calls):
            print("✅ ALL knowledge retrievals succeeded!")
        elif successful_results > 0:
            print(f"⚠️ PARTIAL success: {successful_results}/{len(knowledge_calls)} succeeded")
        else:
            print("❌ ALL knowledge retrievals failed!")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_concurrent_knowledge()