#!/usr/bin/env python3
"""
Direct test of knowledge base components to diagnose the issue.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_knowledge_components():
    """Test knowledge base components directly."""
    
    print("🧪 Testing Knowledge Base Components Directly")
    print("=" * 60)
    
    try:
        # Test 1: Check environment variables
        print("1️⃣ Environment Variables:")
        qdrant_url = os.getenv('QDRANT_URL')
        qdrant_key = os.getenv('QDRANT_API_KEY')
        collection_name = os.getenv('COLLECTION_NAME')
        
        print(f"   QDRANT_URL: {qdrant_url}")
        print(f"   QDRANT_API_KEY: {'*' * 10 if qdrant_key else 'Not set'}")
        print(f"   COLLECTION_NAME: {collection_name}")
        print()
        
        # Test 2: Import and test knowledge search tool
        print("2️⃣ Testing Knowledge Search Tool Import:")
        from tools.knowledge_search import KnowledgeSearchTool
        print("   ✅ KnowledgeSearchTool imported successfully")
        
        # Test 3: Import and test retriever
        print("3️⃣ Testing Retriever Components:")
        from core.retriever import AsyncHybridRetriever
        print("   ✅ AsyncHybridRetriever imported successfully")
        
        # Test 4: Test configuration loading
        print("4️⃣ Testing Configuration:")
        from config.settings import AppConfig
        config = AppConfig.from_env("sqli")
        print(f"   ✅ Config loaded for attack type: sqli")
        print(f"   Collection: {config.qdrant.collection_name}")
        print()
        
        # Test 5: Test knowledge base factory
        print("5️⃣ Testing Knowledge Base Factory:")
        from knowledge.knowledge_base import KnowledgeBaseFactory
        from core.models import ModelFactory
        
        # Create chat model
        chat_model = ModelFactory.create_chat_model(config)
        print("   ✅ Chat model created")
        
        # Test knowledge base creation (with timeout)
        print("   🔄 Testing knowledge base creation...")
        try:
            knowledge_base = KnowledgeBaseFactory.create_knowledge_base(
                config.qdrant, config.azure_openai, config.gemini, 
                config.azure_ml, chat_model, config
            )
            print("   ✅ Knowledge base created successfully")
            
            # Test 6: Test actual search
            print("6️⃣ Testing Direct Knowledge Search:")
            search_tool = KnowledgeSearchTool(knowledge_base.retriever)
            
            # Test search with a specific query
            test_query = "cassandra example #2"
            print(f"   🔍 Searching for: '{test_query}'")
            
            try:
                results = await search_tool.search_knowledge_base(test_query, num_documents=3)
                print(f"   📊 Search returned: {type(results)} with {len(results) if hasattr(results, '__len__') else 'unknown'} items")
                
                if isinstance(results, list):
                    print(f"   ✅ Correct format: list with {len(results)} results")
                    for i, result in enumerate(results[:2]):
                        if isinstance(result, dict):
                            source = result.get('source', 'No source')[:50]
                            content_preview = result.get('content_preview', 'No preview')[:100]
                            print(f"      {i+1}. {source}... | {content_preview}...")
                        else:
                            print(f"      {i+1}. Invalid result type: {type(result)}")
                else:
                    print(f"   ❌ Wrong format: Expected list, got {type(results)}")
                    print(f"   📋 Content: {str(results)[:200]}...")
                    
            except Exception as search_e:
                print(f"   ❌ Search failed: {search_e}")
                import traceback
                traceback.print_exc()
                
        except asyncio.TimeoutError:
            print("   ❌ Knowledge base creation timed out")
        except Exception as kb_e:
            print(f"   ❌ Knowledge base creation failed: {kb_e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_knowledge_components())