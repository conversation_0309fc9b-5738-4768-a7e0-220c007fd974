import asyncio
from config.settings import AppConfig
from config.attack_profiles import ATTACK_PROFILES
from core.models import ModelFactory
from knowledge.knowledge_base import KnowledgeBaseFactory
from agent.agent_factory import AgentFactory
from agent.workflow import AgentWorkflow
import argparse
import asyncio
import os

async def run_agent_async(attack_type: str = None):
    """Initialize and run the threat intelligence agent for specified attack type."""
    
    # Create configuration with attack type
    config = AppConfig.from_env(attack_type)
    attack_profile = config.get_attack_profile()
    
    print(f"Initializing {attack_profile.display_name} Research Agent...")
    config.print_debug_info()
    
    # Create models and knowledge base
    chat_model = ModelFactory.create_chat_model(config)
    knowledge_base = KnowledgeBaseFactory.create_knowledge_base(
        config.qdrant, config.azure_openai, config.gemini,
        config.azure_ml, chat_model, config
    )
    
    retriever = knowledge_base.retriever
    
    # Create attack-specific agent
    agent = AgentFactory.create_agent(
        chat_model, retriever, config.web_search, attack_profile
    )
    
    # Run workflow
    workflow = AgentWorkflow(agent, config)
    await workflow.run_interactive_session()

def main():
    """Main entry point with attack type selection."""
    parser = argparse.ArgumentParser(description="Multi-Attack Threat Intelligence Agent")
    parser.add_argument(
        "--attack-type", "-a",
        choices=list(ATTACK_PROFILES.keys()),
        help="Attack specialization type"
    )
    parser.add_argument(
        "--list-attacks", "-l",
        action="store_true",
        help="List available attack types"
    )
    parser.add_argument(
        "--validate-profile", "-v",
        help="Validate a specific attack profile configuration by its slug"
    )
    parser.add_argument(
        "--collection-check", "-c",
        action="store_true",
        help="Check if Qdrant collections exist for all profiles (requires Qdrant client)"
    )
    
    args = parser.parse_args()
    
    if args.list_attacks:
        print("Available attack specializations:")
        for key, profile in ATTACK_PROFILES.items():
            print(f"  {key}: {profile.display_name}")
        return
    
    if args.validate_profile:
        profile_slug = args.validate_profile
        if profile_slug in ATTACK_PROFILES:
            profile = ATTACK_PROFILES[profile_slug]
            if profile.validate():
                print(f"Profile '{profile_slug}' is VALID.")
            else:
                print(f"Profile '{profile_slug}' is INVALID. Check for missing or empty required fields.")
        else:
            print(f"Unknown profile: '{profile_slug}'. Available: {list(ATTACK_PROFILES.keys())}")
        return

    if args.collection_check:
        print("Checking Qdrant collections for all profiles...")
        # This would require a Qdrant client to actually check. For now, just print status.
        # In a real implementation, you'd iterate through ATTACK_PROFILES and attempt to connect/check collection existence.
        print("Qdrant collection check feature requires Qdrant client integration. (Not implemented in this CLI utility)")
        return
    
    attack_type = args.attack_type or os.getenv("ATTACK_PROFILE", "sqli")
    asyncio.run(run_agent_async(attack_type))

if __name__ == "__main__":
    main()
