from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from langchain_openai import AzureOpenAIEmbeddings
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from agno.knowledge.langchain import Lang<PERSON>hainKnowledgeBase
from config.settings import QdrantConfig, AzureOpenAIConfig, GeminiConfig, AzureMLConfig, AppConfig
from typing import Optional, Union
from core.models import ModelFactory
from core.retriever import AsyncHybridRetriever
from core.reranker import AzureMLReranker

class KnowledgeBaseFactory:
    """Factory for creating and configuring knowledge base components."""
    
    @staticmethod
    def create_qdrant_client(config: QdrantConfig) -> QdrantClient:
        """Create Qdrant client connection with timeout."""
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info(f"Connecting to Qdrant at {config.url}")
        try:
            client = QdrantClient(
                url=config.url,
                api_key=config.api_key,
                timeout=30.0  # 30 second timeout
            )
            
            # Test the connection
            logger.info("Testing Qdrant connection...")
            collections = client.get_collections()
            logger.info(f"Qdrant connection successful, found {len(collections.collections)} collections")
            
            return client
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {str(e)}")
            raise
    
    @staticmethod
    def create_vector_store(qdrant_client: QdrantClient, embeddings: Union[AzureOpenAIEmbeddings, GoogleGenerativeAIEmbeddings], 
                          collection_name: str) -> QdrantVectorStore:
        """Create LangChain Qdrant vector store."""
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info(f"Creating vector store for collection: {collection_name}")
        try:
            # Check if collection exists
            collections = qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if collection_name not in collection_names:
                logger.warning(f"Collection '{collection_name}' not found in Qdrant. Available: {collection_names}")
                logger.warning("Proceeding anyway - collection will be created if needed")
            else:
                logger.info(f"Collection '{collection_name}' found in Qdrant")
            
            vector_store = QdrantVectorStore(
                client=qdrant_client,
                collection_name=collection_name,
                embedding=embeddings
            )
            logger.info("Vector store created successfully")
            return vector_store
            
        except Exception as e:
            logger.error(f"Failed to create vector store: {str(e)}")
            raise
    
    @staticmethod
    def create_hybrid_retriever(azure_model, vector_store: QdrantVectorStore, 
                              qdrant_client: QdrantClient, collection_name: str,
                              reranker: Optional[AzureMLReranker], k: int = 5) -> AsyncHybridRetriever:
        """Create hybrid retriever with all dependencies."""
        return AsyncHybridRetriever(
            azure_model=azure_model,
            vector_store=vector_store,
            qdrant_client=qdrant_client,
            collection_name=collection_name,
            reranker=reranker,
            k=k,
        )
    
    @staticmethod
    def create_knowledge_base(qdrant_config: QdrantConfig, azure_config: AzureOpenAIConfig,
                            gemini_config: GeminiConfig, azure_ml_config: Optional[AzureMLConfig], 
                            chat_model, app_config: AppConfig) -> LangChainKnowledgeBase:
        """Create complete knowledge base setup with model provider selection."""
        
        # Create embeddings based on model provider
        embeddings = ModelFactory.create_embeddings_model(app_config)
        
        # Create Qdrant client and vector store
        qdrant_client = KnowledgeBaseFactory.create_qdrant_client(qdrant_config)
        vector_store = KnowledgeBaseFactory.create_vector_store(
            qdrant_client, embeddings, qdrant_config.collection_name
        )
        
        # Create reranker only if Azure ML config is available
        reranker = AzureMLReranker(azure_ml_config) if azure_ml_config else None
        
        # Create retriever
        retriever = KnowledgeBaseFactory.create_hybrid_retriever(
            azure_model=chat_model,  # Use the selected chat model
            vector_store=vector_store,
            qdrant_client=qdrant_client,
            collection_name=qdrant_config.collection_name,
            reranker=reranker,
            k=5
        )
        
        # Create knowledge base with retriever
        return LangChainKnowledgeBase(retriever=retriever)