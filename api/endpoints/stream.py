"""
FastAPI streaming endpoints for real-time agent interaction.
Provides Server-Sent Events (SSE) for beautiful, informational UI.
"""

import asyncio
import json
import logging
import time
from typing import AsyncGenerator

from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from config.settings import AppConfig
from config.attack_profiles import ATTACK_PROFILES
from core.models import ModelFactory
from knowledge.knowledge_base import KnowledgeBaseFactory
from agent.agent_factory import AgentFactory
from agent.streaming_workflow import StreamingAgentWorkflow
from api.schemas.stream_events import ChatRequest, StreamEvent, EventTypes, ErrorEventData

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["streaming"])

class AgentDependency:
    """Dependency to manage agent lifecycle and configuration."""
    
    def __init__(self):
        self._agents = {}  # Cache agents by attack type
        self._agent_creation_locks = {}  # Prevent concurrent creation
        
    async def get_agent(self, attack_type: str = "sqli") -> StreamingAgentWorkflow:
        """Get or create agent for the specified attack type with extended timeout."""
        
        if attack_type not in ATTACK_PROFILES:
            raise HTTPException(
                status_code=400, 
                detail=f"Unknown attack type: {attack_type}. Available: {list(ATTACK_PROFILES.keys())}"
            )
        
        # Check cache
        if attack_type in self._agents:
            logger.info(f"Using cached agent for {attack_type}")
            return self._agents[attack_type]
        
        # Prevent concurrent creation of the same agent
        if attack_type in self._agent_creation_locks:
            logger.info(f"Waiting for concurrent agent creation for {attack_type}")
            while attack_type in self._agent_creation_locks:
                await asyncio.sleep(0.1)
            if attack_type in self._agents:
                return self._agents[attack_type]
        
        # Mark as being created
        self._agent_creation_locks[attack_type] = True
        
        try:
            logger.info(f"Starting agent creation for {attack_type}...")
            
            # Create configuration with attack type
            config = AppConfig.from_env(attack_type)
            attack_profile = config.get_attack_profile()
            logger.info(f"Configuration loaded for {attack_profile.display_name}")
            
            # Create models with timeout
            logger.info("Creating chat model...")
            chat_model = ModelFactory.create_chat_model(config)
            logger.info("Chat model created successfully")
            
            # Create knowledge base with timeout and fallback
            logger.info("Creating knowledge base...")
            try:
                knowledge_base = await asyncio.wait_for(
                    asyncio.to_thread(
                        KnowledgeBaseFactory.create_knowledge_base,
                        config.qdrant, config.azure_openai, config.gemini,
                        config.azure_ml, chat_model, config
                    ),
                    timeout=60.0  # 60 second timeout for knowledge base
                )
                retriever = knowledge_base.retriever
                logger.info("Knowledge base created successfully")
                
            except asyncio.TimeoutError:
                logger.warning("Knowledge base creation timed out, creating fallback agent without knowledge base")
                # Create a simple fallback retriever
                retriever = None
                
            except Exception as e:
                logger.warning(f"Knowledge base creation failed: {str(e)}, creating fallback agent")
                retriever = None
            
            # Create attack-specific agent
            logger.info("Creating agent...")
            agent = AgentFactory.create_agent(
                chat_model, retriever, config.web_search, attack_profile
            )
            logger.info("Agent created successfully")
            
            # Create streaming workflow
            streaming_workflow = StreamingAgentWorkflow(agent, config)
            
            # Cache the agent
            self._agents[attack_type] = streaming_workflow
            
            logger.info(f"Successfully created and cached agent for {attack_type}")
            return streaming_workflow
            
        except asyncio.TimeoutError:
            logger.error(f"Agent creation timed out for {attack_type}")
            raise HTTPException(
                status_code=504,
                detail=f"Agent initialization timed out for {attack_type}. This usually means the knowledge base connection is slow. Please try again."
            )
        except Exception as e:
            logger.error(f"Failed to create agent for {attack_type}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize agent: {str(e)}"
            )
        finally:
            # Remove creation lock
            if attack_type in self._agent_creation_locks:
                del self._agent_creation_locks[attack_type]

# Global agent dependency instance
agent_dependency = AgentDependency()

async def get_streaming_agent(attack_type: str = "sqli") -> StreamingAgentWorkflow:
    """Dependency function to get streaming agent."""
    return await agent_dependency.get_agent(attack_type)

@router.post("/chat/stream")
async def stream_chat_response(
    request: ChatRequest,
    fastapi_request: Request
) -> EventSourceResponse:
    """
    Stream agent responses in real-time using Server-Sent Events.
    
    This endpoint provides a beautiful, informational streaming interface showing:
    - Agent initialization and configuration
    - LLM token-by-token responses
    - Tool call executions with structured results
    - Search results with titles, URLs, and snippets
    - Knowledge base document previews
    - Agent reasoning and intermediate steps
    - Completion status and metrics
    """
    
    async def event_generator() -> AsyncGenerator[dict, None]:
        """Generate SSE events from agent execution."""
        
        try:
            # Send initialization event
            yield {
                "event": "initialization",
                "data": json.dumps({
                    "message": f"Initializing {request.attack_type or 'sqli'} agent...",
                    "attack_type": request.attack_type or "sqli"
                })
            }
            
            # Get agent for the requested attack type with timeout
            streaming_agent = await asyncio.wait_for(
                get_streaming_agent(request.attack_type or "sqli"),
                timeout=120.0  # 2 minutes for agent initialization
            )
            
            # Send ready event
            yield {
                "event": "agent_ready",
                "data": json.dumps({
                    "message": "Agent ready, starting query processing...",
                    "attack_type": request.attack_type or "sqli"
                })
            }
            
            # Stream agent response
            async for stream_event in streaming_agent.stream_agent_response(request.query):
                # Check if client disconnected
                if await fastapi_request.is_disconnected():
                    logger.info("Client disconnected from stream")
                    break
                
                # Format event for SSE
                yield {
                    "event": stream_event.event,
                    "data": json.dumps(stream_event.data),
                    "id": str(stream_event.timestamp.timestamp())
                }
                
                # Small delay to prevent overwhelming the client
                await asyncio.sleep(0.01)
                
        except asyncio.TimeoutError:
            logger.error("Agent initialization timed out")
            yield {
                "event": "error",
                "data": json.dumps({
                    "message": "Agent initialization timed out. This can happen on first startup while connecting to knowledge base. Please try again.",
                    "error_type": "TimeoutError",
                    "code": 504,
                    "recoverable": True
                })
            }
            
        except HTTPException as he:
            # Re-raise HTTP exceptions
            error_event = StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=he.detail,
                    error_type="HTTPException",
                    code=he.status_code,
                    recoverable=False
                ).model_dump()
            )
            yield {
                "event": error_event.event,
                "data": json.dumps(error_event.data)
            }
            
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Stream error: {str(e)}")
            error_event = StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=f"Streaming failed: {str(e)}",
                    error_type=type(e).__name__,
                    code=500,
                    recoverable=False
                ).model_dump()
            )
            yield {
                "event": error_event.event,
                "data": json.dumps(error_event.data)
            }
    
    return EventSourceResponse(
        event_generator(),
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.get("/chat/models")
async def list_available_models():
    """List available attack types and their configurations."""
    
    models = []
    for attack_type, profile in ATTACK_PROFILES.items():
        models.append({
            "attack_type": attack_type,
            "display_name": profile.display_name,
            "description": profile.instructions[:100] + "..." if len(profile.instructions) > 100 else profile.instructions,
            "specialized_terms": profile.specialized_search_terms[:5],
            "research_phases": profile.research_phases
        })
    
    return {
        "available_models": models,
        "default_model": "sqli"
    }

@router.get("/chat/health")
async def health_check():
    """Health check endpoint for the streaming API."""
    
    try:
        # Try to create a basic config to verify setup
        config = AppConfig.from_env("sqli")
        
        return {
            "status": "healthy",
            "message": "Streaming API is operational",
            "attack_types_available": list(ATTACK_PROFILES.keys()),
            "model_provider": config.model_provider
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Configuration error: {str(e)}",
            "attack_types_available": list(ATTACK_PROFILES.keys())
        }

@router.post("/chat/validate")
async def validate_request(request: ChatRequest):
    """Validate a chat request without executing it."""
    
    if not request.query.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")
    
    if request.attack_type and request.attack_type not in ATTACK_PROFILES:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid attack type: {request.attack_type}. Available: {list(ATTACK_PROFILES.keys())}"
        )
    
    return {
        "valid": True,
        "attack_type": request.attack_type or "sqli",
        "query_length": len(request.query),
        "estimated_tokens": len(request.query.split()) * 1.3  # Rough estimate
    }

@router.post("/chat/warmup")
async def warmup_agents():
    """Pre-initialize agents for faster response times."""
    
    results = {}
    
    for attack_type in ATTACK_PROFILES.keys():
        try:
            logger.info(f"Warming up agent for {attack_type}...")
            start_time = time.time()
            
            agent = await asyncio.wait_for(
                agent_dependency.get_agent(attack_type),
                timeout=120.0
            )
            
            end_time = time.time()
            results[attack_type] = {
                "status": "ready",
                "initialization_time_ms": (end_time - start_time) * 1000,
                "cached": True
            }
            
        except asyncio.TimeoutError:
            results[attack_type] = {
                "status": "timeout",
                "error": "Agent initialization timed out",
                "cached": False
            }
        except Exception as e:
            results[attack_type] = {
                "status": "error", 
                "error": str(e),
                "cached": False
            }
    
    return {
        "message": "Agent warmup completed",
        "results": results,
        "total_ready": sum(1 for r in results.values() if r["status"] == "ready"),
        "total_agents": len(ATTACK_PROFILES)
    }