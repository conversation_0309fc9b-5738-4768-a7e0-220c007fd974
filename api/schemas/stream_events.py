"""
Streaming event schemas for the cybersecurity agent API.
Defines structured events for real-time agent interaction streaming.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Literal, Union, Optional
from datetime import datetime

class StreamEvent(BaseModel):
    """Base model for all streamed events."""
    event: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class AgentStartData(BaseModel):
    """Data for agent initialization event."""
    attack_type: str
    display_name: str
    knowledge_base: str
    specialized_terms: List[str]
    session_id: Optional[str] = None

class LLMTokenData(BaseModel):
    """Data for individual LLM token streaming."""
    token: str
    is_complete: bool = False

class ToolCallStartData(BaseModel):
    """Data for tool execution start event."""
    tool_name: str
    tool_input: Dict[str, Any]
    call_id: Optional[str] = None

class SearchResultData(BaseModel):
    """Data structure for web search results."""
    title: str
    url: str
    snippet: str
    has_full_content: bool = False

class KnowledgeDocumentData(BaseModel):
    """Data structure for knowledge base documents."""
    source: str
    content_preview: str
    metadata: Dict[str, Any]
    relevance_score: Optional[float] = None

class ToolCallResultData(BaseModel):
    """Data for tool execution results."""
    tool_name: str
    call_id: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    # Results can be either search results or knowledge documents
    search_results: Optional[List[SearchResultData]] = None
    knowledge_documents: Optional[List[KnowledgeDocumentData]] = None
    execution_time_ms: Optional[float] = None
    result_summary: Optional[str] = None  # Summary of results (e.g., "Found 5 documents")

class ReasoningStepData(BaseModel):
    """Data for agent reasoning/thinking steps."""
    step_type: Literal[
        "thinking", "planning", "analyzing", "synthesizing", 
        "reasoning", "reasoning_started", "reasoning_completed",
        "knowledge_search", "web_search", "finalizing",
        "think_tool", "analyze_tool", "reasoning_tool"  # New Agno reasoning tool events
    ]
    message: str
    detailed_reasoning: Optional[str] = None  # Additional detailed reasoning content
    confidence: Optional[float] = None
    tool_data: Optional[Dict[str, Any]] = None  # Additional tool-specific data

class AgentCompleteData(BaseModel):
    """Data for agent completion event."""
    final_answer: str
    total_tokens: Optional[int] = None
    total_time_ms: Optional[float] = None
    tools_used: List[str] = []

class ErrorEventData(BaseModel):
    """Data for error events."""
    message: str
    error_type: str
    code: int = Field(default=500)
    recoverable: bool = True

# Union type for all possible event data
StreamEventData = Union[
    AgentStartData,
    LLMTokenData, 
    ToolCallStartData,
    ToolCallResultData,
    ReasoningStepData,
    AgentCompleteData,
    ErrorEventData
]

class ChatRequest(BaseModel):
    """Request model for chat streaming endpoint."""
    query: str
    attack_type: Optional[str] = "sqli"
    session_id: Optional[str] = None
    stream_config: Optional[Dict[str, Any]] = Field(default_factory=dict)

class StreamingResponse(BaseModel):
    """Response wrapper for streaming endpoints."""
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Event type constants for easy reference
class EventTypes:
    AGENT_START = "agent_start"
    LLM_TOKEN = "llm_token"
    TOOL_CALL_START = "tool_call_start"
    TOOL_CALL_RESULT = "tool_call_result"
    REASONING_STEP = "reasoning_step"
    THINKING_STEP = "thinking_step"     # Agent's internal thoughts
    ANALYSIS_STEP = "analysis_step"     # Agent analyzing results
    AGENT_COMPLETE = "agent_complete"
    ERROR = "error"