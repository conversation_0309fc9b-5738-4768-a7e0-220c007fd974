#!/usr/bin/env python3
"""
Test agent tool execution directly to isolate the issue.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_agent_tools():
    """Test agent tools directly through the agent factory."""
    
    print("🔧 Testing Agent Tools Directly")
    print("=" * 60)
    
    try:
        # Step 1: Create configuration
        print("1️⃣ Creating Configuration:")
        from config.settings import AppConfig
        config = AppConfig.from_env("sqli")
        print(f"   ✅ Config loaded for attack type: sqli")
        print(f"   Collection: {config.qdrant.collection_name}")
        print()
        
        # Step 2: Create models and knowledge base
        print("2️⃣ Creating Models and Knowledge Base:")
        from core.models import ModelFactory
        from knowledge.knowledge_base import KnowledgeBaseFactory
        
        chat_model = ModelFactory.create_chat_model(config)
        knowledge_base = KnowledgeBaseFactory.create_knowledge_base(
            config.qdrant, config.azure_openai, config.gemini, 
            config.azure_ml, chat_model, config
        )
        print("   ✅ Models and knowledge base created")
        print()
        
        # Step 3: Create agent tools using factory
        print("3️⃣ Creating Agent Tools via Factory:")
        from agent.agent_factory import AgentFactory
        
        # Create the tools exactly as the agent does
        web_search_tool = AgentFactory.create_web_search_tool(config.web_search)
        knowledge_tool = AgentFactory.create_knowledge_search_tool(knowledge_base.retriever)
        
        print("   ✅ Tools created via agent factory")
        
        # Debug the tool objects
        print(f"   🔍 Knowledge tool type: {type(knowledge_tool)}")
        print(f"   🔍 Knowledge tool attributes: {dir(knowledge_tool)}")
        print(f"   🔍 Web search tool type: {type(web_search_tool)}")
        print()
        
        # Step 4: Test knowledge search tool directly
        print("4️⃣ Testing Knowledge Search Tool:")
        test_query = "cassandra example #2"
        print(f"   🔍 Calling knowledge tool with: '{test_query}'")
        
        try:
            # Call the underlying function directly
            knowledge_result = await knowledge_tool.func(test_query, num_documents=3)
            print(f"   📊 Knowledge tool returned: {type(knowledge_result)}")
            
            if isinstance(knowledge_result, list):
                print(f"   ✅ Correct format: list with {len(knowledge_result)} items")
                for i, item in enumerate(knowledge_result[:2]):
                    if isinstance(item, dict):
                        title = item.get('title', 'No title')[:50]
                        preview = item.get('content_preview', 'No preview')[:100]
                        print(f"      {i+1}. {title}... | {preview}...")
                    else:
                        print(f"      {i+1}. Invalid item type: {type(item)}")
            else:
                print(f"   ❌ Wrong format: Expected list, got {type(knowledge_result)}")
                print(f"   📋 Content preview: {str(knowledge_result)[:200]}...")
                
        except Exception as k_e:
            print(f"   ❌ Knowledge tool failed: {k_e}")
            import traceback
            traceback.print_exc()
        
        print()
        
        # Step 5: Test web search tool directly
        print("5️⃣ Testing Web Search Tool:")
        web_query = "Oracle SQL injection techniques"
        print(f"   🔍 Calling web search tool with: '{web_query}'")
        
        try:
            # Call the underlying function directly
            web_result = await web_search_tool.func(web_query, num_results=2)
            print(f"   📊 Web tool returned: {type(web_result)}")
            
            if isinstance(web_result, list):
                print(f"   ✅ Correct format: list with {len(web_result)} items")
                for i, item in enumerate(web_result[:2]):
                    if isinstance(item, dict):
                        title = item.get('title', 'No title')[:50]
                        link = item.get('link', 'No link')[:50]
                        print(f"      {i+1}. {title}... | {link}...")
                    else:
                        print(f"      {i+1}. Invalid item type: {type(item)}")
            elif web_result is None:
                print(f"   ❌ Tool returned None - possible connection or configuration issue")
            else:
                print(f"   ❌ Wrong format: Expected list, got {type(web_result)}")
                print(f"   📋 Content preview: {str(web_result)[:200]}...")
                
        except Exception as w_e:
            print(f"   ❌ Web search tool failed: {w_e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent_tools())