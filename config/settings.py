import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Optional
from typing import Dict, List
from config.attack_profiles import AttackProfile, ATTACK_PROFILES

# Load environment variables
load_dotenv()

@dataclass
class QdrantConfig:
    """Configuration for Qdrant vector database."""
    url: str
    api_key: str
    collection_name: str
    
    @classmethod
    def from_env(cls) -> 'QdrantConfig':
        url = os.getenv("QDRANT_URL")
        api_key = os.getenv("QDRANT_API_KEY")
        collection_name = os.getenv("COLLECTION_NAME")
        
        if not all([url, api_key, collection_name]):
            raise ValueError("QDRANT_URL, QDRANT_API_KEY, and COLLECTION_NAME must be set")
        
        return cls(url=url, api_key=api_key, collection_name=collection_name)

@dataclass
class AzureOpenAIConfig:
    """Configuration for Azure OpenAI services."""
    endpoint: str
    api_key: str
    chat_deployment: str
    embedding_deployment: str
    
    @classmethod
    def from_env(cls) -> 'AzureOpenAIConfig':
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        api_key = os.getenv("AZURE_OPENAI_API_KEY")
        chat_deployment = os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT")
        embedding_deployment = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")
        
        if not all([endpoint, api_key, chat_deployment, embedding_deployment]):
            raise ValueError("All Azure OpenAI environment variables must be set")
        
        return cls(
            endpoint=endpoint,
            api_key=api_key,
            chat_deployment=chat_deployment,
            embedding_deployment=embedding_deployment
        )

@dataclass
class WebSearchConfig:
    """Configuration for web search functionality."""
    backend: str = "bing"
    use_jina_reader: bool = False
    timeout_seconds: int = 10
    
    @classmethod
    def from_env(cls) -> 'WebSearchConfig':
        backend = os.getenv("WEB_SEARCH_BACKEND", "bing")
        use_jina_reader = os.getenv("USE_JINA_READER", "False").lower() == "true"
        timeout_seconds = int(os.getenv("WEB_SEARCH_TIMEOUT_SECONDS", "10"))
        
        return cls(backend=backend, use_jina_reader=use_jina_reader, timeout_seconds=timeout_seconds)

@dataclass
class AzureMLConfig:
    """Configuration for Azure ML reranking service."""
    endpoint: str
    api_key: str
    
    @classmethod
    def from_env(cls) -> Optional['AzureMLConfig']:
        endpoint = os.getenv("AZURE_ML_RERANK_ENDPOINT")
        api_key = os.getenv("AZURE_ML_RERANK_KEY")
        
        # Only create the config if both are present. Do not use hardcoded defaults for keys.
        if not endpoint or not api_key:
            print("INFO: Azure ML Reranker not configured (environment variables not set).")
            return None
        
        return cls(endpoint=endpoint, api_key=api_key)

@dataclass
class GeminiConfig:
    """Configuration for Google Gemini services."""
    api_key: Optional[str]
    model_provider: str = "google_ai_studio"  # or "vertex_ai"
    chat_model: str = "models/gemini-2.0-flash"
    embedding_model: str = "models/text-embedding-004"
    
    # Vertex AI specific (optional)
    project_id: Optional[str] = None
    location: str = "us-central1"
    use_vertex_ai: bool = False
    
    @classmethod
    def from_env(cls) -> 'GeminiConfig':
        api_key = os.getenv("GEMINI_API_KEY")
        model_provider = os.getenv("GEMINI_MODEL_PROVIDER", "google_ai_studio")
        chat_model = os.getenv("GEMINI_CHAT_MODEL", "models/gemini-2.0-flash")
        embedding_model = os.getenv("GEMINI_EMBEDDING_MODEL", "models/text-embedding-004")
        
        # Vertex AI configuration
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1")
        use_vertex_ai = os.getenv("GOOGLE_GENAI_USE_VERTEXAI", "false").lower() == "true"
        
        if model_provider == "vertex_ai" and not all([project_id]):
            raise ValueError("GOOGLE_CLOUD_PROJECT must be set when using vertex_ai provider")
        
        if model_provider == "google_ai_studio" and not api_key:
            raise ValueError("GEMINI_API_KEY must be set when using google_ai_studio provider")
        
        return cls(
            api_key=api_key,
            model_provider=model_provider,
            chat_model=chat_model,
            embedding_model=embedding_model,
            project_id=project_id,
            location=location,
            use_vertex_ai=use_vertex_ai
        )

@dataclass
class AppConfig:
    """Main application configuration."""
    qdrant: QdrantConfig
    azure_openai: AzureOpenAIConfig
    gemini: GeminiConfig
    web_search: WebSearchConfig
    azure_ml: Optional[AzureMLConfig]  # Changed to Optional
    model_provider: str = "azure_openai"  # "azure_openai" or "gemini"
    debug: bool = True
    
    # Attack-specific configuration
    attack_profile_slug: str = "sqli"  # Default to SQL injection
    auto_collection_name: bool = True  # Auto-set collection based on profile

    @classmethod
    def from_env(cls, attack_type: str = None) -> 'AppConfig':
        """Create config with optional attack type override."""
        model_provider = os.getenv("MODEL_PROVIDER", "azure_openai")
        attack_profile_slug = attack_type or os.getenv("ATTACK_PROFILE", "sqli")

        # Validate attack profile exists
        if attack_profile_slug not in ATTACK_PROFILES:
            raise ValueError(f"Unknown attack profile: {attack_profile_slug}. Available: {list(ATTACK_PROFILES.keys())}")
        
        config = cls(
            qdrant=QdrantConfig.from_env(),
            azure_openai=AzureOpenAIConfig.from_env(),
            gemini=GeminiConfig.from_env(),
            web_search=WebSearchConfig.from_env(),
            azure_ml=AzureMLConfig.from_env(),
            model_provider=model_provider,
            attack_profile_slug=attack_profile_slug
        )

        # Auto-set collection name if enabled
        if config.auto_collection_name:
            profile = ATTACK_PROFILES[attack_profile_slug]
            config.qdrant.collection_name = profile.qdrant_collection
        
        # Validate Qdrant collection name is set
        if not config.qdrant.collection_name:
            raise ValueError("Qdrant COLLECTION_NAME must be set, either manually or via ATTACK_PROFILE auto-assignment.")

        return config
    
    def get_attack_profile(self) -> AttackProfile:
        """Get the current attack profile configuration."""
        return ATTACK_PROFILES[self.attack_profile_slug]

    def print_debug_info(self):
        """Print debug information about the configuration."""
        if self.debug:
            print(f"DEBUG: Model Provider: {self.model_provider}")
            print(f"DEBUG: Connecting to Qdrant collection '{self.qdrant.collection_name}' at {self.qdrant.url}")
            print(f"DEBUG: Attack Profile: {self.attack_profile_slug}")
            
            if self.model_provider == "gemini":
                print(f"DEBUG: Using Gemini model: {self.gemini.chat_model}")
                print(f"DEBUG: Gemini provider: {self.gemini.model_provider}")
                print(f"DEBUG: Gemini embedding model: {self.gemini.embedding_model}")
                if self.gemini.model_provider == "vertex_ai":
                    print(f"DEBUG: Vertex AI project: {self.gemini.project_id}")
                    print(f"DEBUG: Vertex AI location: {self.gemini.location}")
            else:
                print(f"DEBUG: Using Azure OpenAI embedding model: {self.azure_openai.embedding_deployment}")
            
            print(f"DEBUG: Web Search Backend: {self.web_search.backend}, Use Jina Reader: {self.web_search.use_jina_reader}, Timeout: {self.web_search.timeout_seconds}s")
            if self.azure_ml:
                print(f"DEBUG: Azure ML Rerank Endpoint: {self.azure_ml.endpoint}")
            else:
                print("DEBUG: Azure ML Reranker is not configured.")

# Global configuration instance
config = AppConfig.from_env()