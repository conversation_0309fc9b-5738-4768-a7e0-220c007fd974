from config.attack_profiles import AttackProfile

RCE_PROFILE = AttackProfile(
    attack_type="rce",
    display_name="Remote Code Execution",
    qdrant_collection="rce_knowledge_base",
    
    description="You specialize in Remote Code Execution (RCE) vulnerabilities, focusing on server-side code execution and command injection attacks. Your expertise includes analyzing deserialization and template injection, and investigating various OS command execution vectors.",
    
    instructions=[
        "Focus on server-side code execution vulnerabilities.",
        "Analyze command injection and code injection attacks.",
        "Research deserialization and template injection.",
        "Investigate OS command execution vectors and their variations.",
        "Prioritize recent RCE exploits and post-exploitation techniques.",
        "Synthesize information from both internal knowledge base and real-time web searches, ensuring all findings are verified and cited."
    ],
    
    expected_output="""
    ## RCE Threat Intelligence Report

    ## Executive Summary
    {{Brief overview of RCE threat landscape and key findings}}

    ## Attack Vectors
    {{For EACH distinct RCE technique, create dedicated sub-sections}}

    #### Technical Mechanism
    {{Detailed explanation of how the RCE attack works}}

    #### Prerequisites
    - **Application Requirements:** {{e.g., vulnerable function calls, outdated libraries}}
    - **Environment Conditions:** {{e.g., specific operating system, accessible services}}
    - **Attacker Prerequisites:** {{e.g., network access, knowledge of application's internal workings}}

    #### Exploitation Examples
    ```
    {{Attack-specific payload examples, clearly commented}}
    ```

    ## RCE-Specific Mitigation Strategies
    ### Input Validation and Whitelisting
    ### Sandboxing and Containerization
    ### Privilege Separation
    ### Code Review and Static Analysis

    ## RCE Detection and Forensics
    ### Process Monitoring
    ### System Call Analysis
    ### Network Traffic Analysis
    ### File System Monitoring

    ## Threat Intelligence Sources
    - [1] {{Title}} - {{URL}}
    - [2] {{Title}} - {{URL}}
    """,
    
    research_phases={
        "Entry Point Analysis": "Identify RCE attack surfaces and input vectors, including web application, API, and network services.",
        "Execution Context": "Analyze privilege escalation techniques, environment escape methods, and persistence mechanisms.",
        "Payload Crafting": "Research platform-specific execution techniques, encoding, and evasion methods.",
        "Persistence Methods": "Evaluate post-exploitation techniques, backdoors, and rootkit detection."
    },
    
    specialized_search_terms=[
        "command injection", "code execution", "deserialization attack",
        "template injection", "eval vulnerability", "system() abuse",
        "shell injection", "unsafe deserialization", "RCE exploit",
        "remote code execution prevention", "RCE detection methods"
    ],
    
    mitigation_categories=[
        "Input Validation and Whitelisting", 
        "Sandboxing and Containerization",
        "Privilege Separation",
        "Code Review and Static Analysis",
        "Secure Configuration Management"
    ],
    
    detection_categories=[
        "Process Monitoring",
        "System Call Analysis", 
        "Network Traffic Analysis",
        "File System Monitoring",
        "Endpoint Detection and Response (EDR) alerts"
    ]
)