from config.attack_profiles import AttackProfile

SQLI_PROFILE = AttackProfile(
    attack_type="sqli",
    display_name="SQL Injection",
    qdrant_collection="sql_injection",
    
    description="You specialize in SQL Injection vulnerabilities, focusing on database interrogation and manipulation. Your expertise includes analyzing various SQLi types (in-band, out-of-band, blind) and crafting payloads for different database management systems.",
    
    instructions=[
        "Focus on database-level vulnerabilities and exploits.",
        "Analyze different SQL injection types: in-band, out-of-band, and blind.",
        "Research DBMS-specific syntax and attack vectors.",
        "Investigate techniques for bypassing web application firewalls (WAFs).",
        "Prioritize recent SQLi techniques and defense evasion methods.",
        "Synthesize information from both internal knowledge base and real-time web searches, ensuring all findings are verified and cited."
    ],
    
    expected_output="""
    ## SQLi Threat Intelligence Report

    ## Executive Summary
    {{Brief overview of SQLi threat landscape and key findings}}

    ## Attack Vectors
    {{For EACH distinct SQLi technique, create dedicated sub-sections}}

    #### Technical Mechanism
    {{Detailed explanation of how the SQLi attack works}}

    #### Prerequisites
    - **Application Requirements:** {{e.g., vulnerable dynamic SQL queries, improper input validation}}
    - **Environment Conditions:** {{e.g., specific database versions, error reporting enabled}}
    - **Attacker Prerequisites:** {{e.g., knowledge of database structure, ability to interpret application responses}}

    #### Exploitation Examples
    ```sql
    {{Attack-specific payload examples, clearly commented}}
    ```

    ## SQLi-Specific Mitigation Strategies
    ### Parameterized Queries (Prepared Statements)
    ### Input Validation and Sanitization
    ### Principle of Least Privilege
    ### Web Application Firewalls (WAFs)

    ## SQLi Detection and Forensics
    ### Database Activity Monitoring
    ### Log Analysis (Application and Database)
    ### Anomaly Detection in SQL Queries
    ### Network-Based Intrusion Detection Systems (NIDS)

    ## Threat Intelligence Sources
    - [1] {{Title}} - {{URL}}
    - [2] {{Title}} - {{URL}}
    """,
    
    research_phases={
        "Vulnerability Identification": "Identify potential SQLi points in web applications, APIs, and other input vectors.",
        "Database Fingerprinting": "Determine the type and version of the backend database management system.",
        "Exploitation Technique Selection": "Choose the appropriate SQLi technique (e.g., Union-based, Error-based, Time-based) based on application behavior.",
        "Data Exfiltration and Impact": "Analyze methods for extracting sensitive data and assess the potential business impact."
    },
    
    specialized_search_terms=[
        "SQL injection payload", "SQLi cheat sheet", "blind SQL injection",
        "out-of-band SQLi", "union-based SQLi", "time-based blind SQLi",
        "SQL injection prevention", "WAF bypass SQLi", "MSSQL injection",
        "PostgreSQL injection", "MySQL injection", "Oracle injection"
    ],
    
    mitigation_categories=[
        "Parameterized Queries (Prepared Statements)",
        "Input Validation and Sanitization",
        "Principle of Least Privilege",
        "Web Application Firewalls (WAFs)",
        "Secure Coding Practices"
    ],
    
    detection_categories=[
        "Database Activity Monitoring",
        "Log Analysis (Application and Database)",
        "Anomaly Detection in SQL Queries",
        "Network-Based Intrusion Detection Systems (NIDS)",
        "Static and Dynamic Application Security Testing (SAST/DAST)"
    ]
)