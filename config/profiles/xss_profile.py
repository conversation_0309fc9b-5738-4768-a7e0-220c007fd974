from config.attack_profiles import AttackProfile

XSS_PROFILE = AttackProfile(
    attack_type="xss",
    display_name="Cross-Site Scripting (XSS)",
    qdrant_collection="xss_knowledge_base",
    
    description="You specialize in Cross-Site Scripting vulnerabilities, focusing on client-side code injection and browser-based attacks. Your expertise includes analyzing DOM manipulation, researching Content Security Policy (CSP) bypasses, and investigating stored, reflected, and DOM-based XSS variants.",
    
    instructions=[
        "Focus on client-side code injection vulnerabilities.",
        "Analyze DOM manipulation and browser-side attacks.",
        "Research Content Security Policy (CSP) bypasses.",
        "Investigate stored, reflected, and DOM-based XSS variants.",
        "Prioritize recent XSS techniques and browser-specific exploitation methods.",
        "Synthesize information from both internal knowledge base and real-time web searches, ensuring all findings are verified and cited."
    ],
    
    expected_output="""
    ## XSS Threat Intelligence Report

    ## Executive Summary
    {{Brief overview of XSS threat landscape and key findings}}

    ## Attack Vectors
    {{For EACH distinct XSS technique, create dedicated sub-sections}}

    #### Technical Mechanism
    {{Detailed explanation of how the XSS attack works}}

    #### Prerequisites
    - **Application Requirements:** {{e.g., vulnerable input validation, lack of output encoding}}
    - **Environment Conditions:** {{e.g., specific browser versions, misconfigured web servers}}
    - **Attacker Prerequisites:** {{e.g., knowledge of target application's input processing, social engineering skills}}

    #### Exploitation Examples
    ```
    {{Attack-specific payload examples, clearly commented}}
    ```

    ## XSS-Specific Mitigation Strategies
    ### Input Validation and Sanitization
    ### Output Encoding Strategies
    ### Content Security Policy (CSP)
    ### Framework-Specific Protections

    ## XSS Detection and Forensics
    ### Client-Side Monitoring
    ### Web Application Firewalls (WAF)
    ### Browser Security Extensions
    ### Log Analysis Patterns

    ## Threat Intelligence Sources
    - [1] {{Title}} - {{URL}}
    - [2] {{Title}} - {{URL}}
    """,
    
    research_phases={
        "Vector Classification": "Identify XSS types: stored, reflected, DOM-based, and their prevalence.",
        "Payload Analysis": "Research encoding techniques, obfuscation methods, and filter bypasses.", 
        "Browser Targeting": "Analyze browser-specific XSS exploitation methods and their limitations.",
        "Defense Evaluation": "Review CSP directives, input validation best practices, and output encoding standards."
    },
    
    specialized_search_terms=[
        "XSS payload", "DOM manipulation", "CSP bypass", 
        "reflected XSS", "stored XSS", "DOM-based XSS", "innerHTML vulnerability",
        "JavaScript injection", "HTML entity encoding", "XSS filter bypass",
        "cross-site scripting prevention", "XSS detection tools"
    ],
    
    mitigation_categories=[
        "Input Validation and Sanitization",
        "Output Encoding Strategies", 
        "Content Security Policy (CSP)",
        "Framework-Specific Protections",
        "Secure Development Practices"
    ],
    
    detection_categories=[
        "Client-Side Monitoring",
        "Web Application Firewalls (WAF)",
        "Browser Security Extensions",
        "Log Analysis Patterns",
        "Security Information and Event Management (SIEM) rules"
    ]
)