from dataclasses import dataclass
from typing import Dict, List, Optional
import os

@dataclass
class AttackProfile:
    """Configuration profile for specific attack types."""
    attack_type: str
    display_name: str
    
    # Knowledge Base Configuration
    qdrant_collection: str
    
    # Agent Behavior Configuration
    description: str
    instructions: List[str]
    expected_output: str
    
    # Research Configuration
    research_phases: Dict[str, str]
    specialized_search_terms: List[str]
    mitigation_categories: List[str]
    detection_categories: List[str]
    
    # Tool Configuration
    web_search_timeout: int = 10
    knowledge_search_priority: float = 0.8

    def validate(self) -> bool:
        """Validate profile configuration completeness."""
        required_fields = [
            'attack_type', 'display_name', 'qdrant_collection',
            'description', 'instructions', 'expected_output',
            'research_phases', 'specialized_search_terms',
            'mitigation_categories', 'detection_categories'
        ]
        
        # Check if all required fields are present and not empty (if applicable)
        for field in required_fields:
            value = getattr(self, field)
            if value is None:
                return False
            if isinstance(value, (str, list, dict)) and not value:
                return False
        return True

    def __post_init__(self):
        """Validate profile after initialization."""
        if not self.validate():
            raise ValueError(f"Invalid profile configuration for {self.attack_type}. Missing or empty required fields.")

# Import attack-specific profiles
# These will be created in config/profiles/
from config.profiles.xss_profile import XSS_PROFILE
from config.profiles.rce_profile import RCE_PROFILE
from config.profiles.sqli_profile import SQLI_PROFILE
# from config.profiles.lfi_profile import LFI_PROFILE

# Define attack-specific profiles
ATTACK_PROFILES = {
    "xss": XSS_PROFILE,
    "rce": RCE_PROFILE,
    "sqli": SQLI_PROFILE,
    # "lfi": LFI_PROFILE,
}