from textwrap import dedent
from typing import Dict, List

class AttackSpecificPrompts:
    """Dynamic prompt templates for different attack types."""
    
    @staticmethod
    def get_agent_description(attack_type: str, attack_display_name: str) -> str:
        return dedent(f"""\
            You are an elite Cyber Threat Intelligence Analyst specializing in {attack_display_name}.
            Your primary function is to produce authoritative, accurate, and actionable intelligence
            on {attack_type.upper()} vulnerabilities, exploits, and defense mechanisms.

            You operate by fusing a static, internal Knowledge Base with dynamic, real-time data
            retrieved from the open web using your `web_search` tool. Your core directive is not
            just to answer, but to VERIFY, SYNTHESIZE, and CITE with specific focus on {attack_display_name}
            attack vectors, payloads, and mitigation strategies.
        """)
    
    @staticmethod
    def get_specialized_instructions(attack_type: str, research_phases: Dict[str, str]) -> str:
        # Check if research_phases is empty or None to avoid errors
        if not research_phases:
            research_phases_str = "No specific research phases defined for this attack type."
        else:
            research_phases_str = chr(10).join([f"- **{phase}:** {description}" for phase, description in research_phases.items()])

        return dedent(f"""\
            **{attack_type.upper()}-Specific Research Guidelines:**
            1. **Attack Vector Identification:** Focus on {attack_type.upper()}-specific entry points and vulnerabilities
            2. **Payload Analysis:** Examine {attack_type.upper()} payloads, their variations, and effectiveness
            3. **Impact Assessment:** Analyze the specific business and technical impacts of {attack_type.upper()} attacks
            4. **Defense Mechanisms:** Research {attack_type.upper()}-specific detection and prevention techniques
            
            **Research Phases for {attack_type.upper()}:**
            {research_phases_str}
        """)
    
    @staticmethod
    def get_attack_specific_output_template(attack_type: str, mitigation_categories: List[str], 
                                          detection_categories: List[str]) -> str:
        mitigation_sections = "\n".join([f"### {cat}" for cat in mitigation_categories])
        detection_sections = "\n".join([f"### {cat}" for cat in detection_categories])
        
        return dedent(f"""\
            ## {attack_type.upper()} Threat Intelligence Report

            ## Executive Summary
            {{Brief overview of {attack_type.upper()} threat landscape and key findings}}

            ## Attack Vectors
            {{For EACH distinct {attack_type.upper()} technique, create dedicated sub-sections}}

            #### Technical Mechanism
            {{Detailed explanation of how the {attack_type.upper()} attack works}}

            #### Prerequisites
            - **Application Requirements:** {{e.g., vulnerable input validation}}
            - **Environment Conditions:** {{e.g., specific software versions}}
            - **Attacker Prerequisites:** {{e.g., knowledge requirements}}

            #### Exploitation Examples
            ```
            {{Attack-specific payload examples}}
            ```

            ## {attack_type.upper()}-Specific Mitigation Strategies
            {mitigation_sections}

            ## {attack_type.upper()} Detection and Forensics
            {detection_sections}

            ## Threat Intelligence Sources
            - [1] {{Title}} - {{URL}}
            - [2] {{Title}} - {{URL}}
        """)