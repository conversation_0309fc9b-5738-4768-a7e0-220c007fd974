"""
FastAPI application for the cybersecurity threat intelligence agent.
Provides streaming API endpoints for real-time agent interaction.
"""

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from api.endpoints.stream import router as stream_router
from config.settings import AppConfig
from config.attack_profiles import ATTACK_PROFILES

# Create FastAPI application
app = FastAPI(
    title="Cybersecurity Threat Intelligence Agent API",
    description="Streaming API for multi-attack cybersecurity research agent with real-time insights",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure based on your frontend domain
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

# Include streaming endpoints
app.include_router(stream_router)

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Cybersecurity Threat Intelligence Agent API",
        "version": "1.0.0",
        "features": [
            "Real-time agent streaming",
            "Multi-attack specializations",
            "Knowledge base integration", 
            "Web search capabilities",
            "Beautiful UI-optimized events"
        ],
        "available_attacks": list(ATTACK_PROFILES.keys()),
        "endpoints": {
            "streaming": "/api/v1/chat/stream",
            "models": "/api/v1/chat/models",
            "health": "/api/v1/chat/health",
            "validate": "/api/v1/chat/validate",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health():
    """Basic health check endpoint."""
    try:
        # Test basic configuration
        config = AppConfig.from_env("sqli")
        
        return {
            "status": "healthy",
            "message": "API is operational",
            "model_provider": config.model_provider,
            "attack_types": len(ATTACK_PROFILES)
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "message": f"Configuration error: {str(e)}"
            }
        )

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for better error responses."""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "type": type(exc).__name__
        }
    )

if __name__ == "__main__":
    # Development server configuration
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )