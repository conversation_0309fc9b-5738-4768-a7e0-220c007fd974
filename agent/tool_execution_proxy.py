"""
Tool Execution Proxy - Intercepts all Agno tool calls to ensure consistent execution.

This module provides a comprehensive solution to the Agno framework's inconsistent
tool execution behavior by patching the framework at runtime.
"""

import asyncio
import logging
import threading
import concurrent.futures
import functools
from typing import Any, Dict, Callable
from agno.tools.function import Function

logger = logging.getLogger(__name__)

# Global registry to track our tool implementations
TOOL_REGISTRY: Dict[str, Callable] = {}
EXECUTION_LOCK = threading.RLock()

def register_tool_implementation(tool_name: str, implementation: Callable):
    """Register a reliable tool implementation."""
    with EXECUTION_LOCK:
        TOOL_REGISTRY[tool_name] = implementation
        logger.info(f"🔧 [PROXY] Registered reliable implementation for tool: {tool_name}")

def create_tool_execution_wrapper(original_execute_method):
    """Create a wrapper for <PERSON><PERSON>'s tool execute method."""
    
    @functools.wraps(original_execute_method)
    def execute_wrapper(self, *args, **kwargs):
        tool_name = getattr(self, 'name', 'unknown')
        
        # Check if we have a reliable implementation for this tool
        with EXECUTION_LOCK:
            reliable_impl = TOOL_REGISTRY.get(tool_name)
        
        if reliable_impl:
            logger.info(f"🔄 [PROXY] Intercepting {tool_name} call, using reliable implementation")
            
            try:
                # Extract arguments from the tool call
                if args and isinstance(args[0], dict):
                    # Arguments passed as dictionary
                    tool_args = args[0]
                elif kwargs:
                    # Arguments passed as keyword arguments
                    tool_args = kwargs
                else:
                    # No arguments
                    tool_args = {}
                
                # Call our reliable implementation
                result = reliable_impl(**tool_args)
                logger.info(f"✅ [PROXY] {tool_name} executed successfully via proxy")
                return result
                
            except Exception as e:
                logger.error(f"❌ [PROXY] {tool_name} failed via proxy: {e}")
                # Fall back to original implementation
                pass
        
        # Fall back to original Agno execution
        logger.debug(f"🔄 [PROXY] Using original Agno execution for {tool_name}")
        return original_execute_method(self, *args, **kwargs)
    
    return execute_wrapper

def patch_agno_tool_execution():
    """Patch Agno's Function.execute method to use our proxy."""
    try:
        # Check if Function has execute method
        if not hasattr(Function, 'execute'):
            logger.error("❌ [PROXY] Function class does not have 'execute' method")
            # Try to find the correct method name
            function_methods = [attr for attr in dir(Function) if not attr.startswith('_') and callable(getattr(Function, attr, None))]
            logger.info(f"🔍 [PROXY] Available Function methods: {function_methods}")
            return False
        
        # Store the original execute method
        original_execute = Function.execute
        
        # Create and apply the wrapper
        Function.execute = create_tool_execution_wrapper(original_execute)
        
        logger.info("🔧 [PROXY] Successfully patched Agno Function.execute method")
        return True
        
    except Exception as e:
        logger.error(f"❌ [PROXY] Failed to patch Agno tool execution: {e}")
        # Log more details about the Function class
        try:
            logger.info(f"🔍 [PROXY] Function class attributes: {dir(Function)}")
            if hasattr(Function, '__dict__'):
                logger.info(f"🔍 [PROXY] Function instance dict: {Function.__dict__}")
        except Exception as debug_e:
            logger.error(f"❌ [PROXY] Could not debug Function class: {debug_e}")
        return False

def create_reliable_web_search_impl(web_search_client):
    """Create a reliable web search implementation."""
    
    def reliable_web_search(query: str, num_results: int = 5) -> list[dict]:
        """Reliable web search implementation with comprehensive error handling."""
        logger.info(f"🌐 [RELIABLE] Web search called: '{query}', num_results: {num_results}")
        
        try:
            # More robust async execution
            def run_web_search():
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(web_search_client.asearch(query, num_results))
                        logger.info(f"✅ [RELIABLE] Web search completed, got: {type(result)}")
                        if hasattr(result, '__len__'):
                            logger.info(f"🌐 [RELIABLE] Web search returned {len(result)} results")
                        return result
                    finally:
                        loop.close()
                except Exception as inner_e:
                    logger.error(f"❌ [RELIABLE] Inner web search failed: {inner_e}")
                    raise inner_e
            
            # Try direct execution first
            try:
                docs = asyncio.run(web_search_client.asearch(query, num_results))
                logger.info(f"✅ [RELIABLE] Direct web search succeeded, got: {type(docs)}")
            except RuntimeError as runtime_e:
                if "cannot be called from a running event loop" in str(runtime_e):
                    logger.warning(f"⚠️ [RELIABLE] Event loop conflict, using thread executor")
                    # Use thread executor when event loop conflicts occur
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_web_search)
                        docs = future.result(timeout=30)  # 30 second timeout
                        logger.info(f"✅ [RELIABLE] Thread executor succeeded, got: {type(docs)}")
                else:
                    raise runtime_e
            
            # Convert to expected format
            results = []
            for doc in docs:
                results.append({
                    "title": doc.metadata.get("title"),
                    "link": doc.metadata.get("source"),
                    "snippet": doc.metadata.get("snippet"),
                    "content": doc.page_content
                })
            
            logger.info(f"🔓 [RELIABLE] Web search completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"❌ [RELIABLE] Web search failed: {type(e).__name__}: {e}")
            return [{
                "title": "Web Search Failed",
                "link": "",
                "snippet": f"Web search failed: {str(e)}",
                "content": f"Web search failed: {str(e)}"
            }]
    
    return reliable_web_search

def create_reliable_knowledge_search_impl(knowledge_tool):
    """Create a reliable knowledge search implementation."""
    
    def reliable_knowledge_search(query: str, num_documents: int = 5) -> list[dict]:
        """Reliable knowledge search implementation with comprehensive error handling."""
        logger.info(f"🔍 [RELIABLE] Knowledge search called: '{query}', num_documents: {num_documents}")
        
        try:
            # More robust async execution
            def run_knowledge_search():
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(knowledge_tool.search_knowledge_base(query, num_documents))
                        logger.info(f"✅ [RELIABLE] Knowledge search completed, got: {type(result)}")
                        if isinstance(result, list):
                            logger.info(f"📚 [RELIABLE] Knowledge search returned {len(result)} documents")
                        return result
                    finally:
                        loop.close()
                except Exception as inner_e:
                    logger.error(f"❌ [RELIABLE] Inner knowledge search failed: {inner_e}")
                    raise inner_e
            
            # Try direct execution first
            try:
                result = asyncio.run(knowledge_tool.search_knowledge_base(query, num_documents))
                logger.info(f"✅ [RELIABLE] Direct knowledge search succeeded, got: {type(result)}")
                logger.info(f"🔓 [RELIABLE] Knowledge search completed successfully")
                return result
            except RuntimeError as runtime_e:
                if "cannot be called from a running event loop" in str(runtime_e):
                    logger.warning(f"⚠️ [RELIABLE] Event loop conflict, using thread executor")
                    # Use thread executor when event loop conflicts occur
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_knowledge_search)
                        result = future.result(timeout=30)  # 30 second timeout
                        logger.info(f"✅ [RELIABLE] Thread executor succeeded, got: {type(result)}")
                        logger.info(f"🔓 [RELIABLE] Knowledge search completed successfully")
                        return result
                else:
                    raise runtime_e
                    
        except Exception as e:
            logger.error(f"❌ [RELIABLE] Knowledge search failed: {type(e).__name__}: {e}")
            
            # Return error document instead of None
            return [{
                "source": "Knowledge Search Error",
                "title": "Search Failed",
                "content": f"Knowledge search encountered an error: {str(e)}",
                "content_preview": f"Knowledge search failed: {str(e)}",
                "metadata": {"error": True, "error_type": type(e).__name__},
                "relevance_score": 0.0
            }]
    
    return reliable_knowledge_search