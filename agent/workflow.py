import asyncio
from agno.agent import Agent
from config.settings import AppConfig
from config.attack_profiles import ATTACK_PROFILES
from agno.agent import Agent
import asyncio

class AgentWorkflow:
    """Main workflow for the SQL injection research agent."""
    
    def __init__(self, agent: Agent, config: AppConfig):
        self.agent = agent
        self.config = config
        self.attack_profile = config.get_attack_profile()
    
    async def run_interactive_session(self):
        """Run interactive session with attack-specific context."""
        print(f"{self.attack_profile.display_name} Research Agent initialized.")
        print(f"Attack Specialty: {self.attack_profile.attack_type.upper()}")
        print(f"Knowledge Base: {self.config.qdrant.collection_name}")
        print(f"Specialized Terms: {', '.join(self.attack_profile.specialized_search_terms[:3])}...")
        print("Type 'exit' to quit, 'switch <attack_type>' to change specialty.")
        
        # In a real application, you'd check Qdrant connection here
        # For now, we'll assume it's configured correctly or handle errors later.
        
        while True:
            query = await asyncio.to_thread(input, f"\n[{self.attack_profile.attack_type.upper()}] User: ")
            
            if query.lower() in ["exit", "quit"]:
                break
            elif query.lower().startswith("switch "):
                new_attack_type = query.split()[1].lower()
                if new_attack_type in ATTACK_PROFILES:
                    print(f"Switching to {new_attack_type.upper()} specialization...")
                    # Would need to reinitialize agent with new profile
                    print("Feature coming soon - restart with ATTACK_PROFILE environment variable")
                else:
                    print(f"Unknown attack type. Available: {list(ATTACK_PROFILES.keys())}")
                continue
            
            print(f"Searching {self.attack_profile.display_name} knowledge base...")
            await self.agent.aprint_response(query, stream=True, stream_intermediate_steps=True)

    def run_sync(self):
        """Run the workflow synchronously."""
        asyncio.run(self.run_interactive_session())