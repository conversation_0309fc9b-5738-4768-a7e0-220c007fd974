"""
Streaming workflow wrapper for the cybersecurity agent.
Captures and structures agent events for real-time streaming to UI.
"""

import asyncio
import json
import time
import uuid
import inspect
import logging
import threading
from typing import AsyncGenerator, Dict, Any, Optional
from datetime import datetime

from agno.agent import Agent
from agent.tool_interceptor import GLOBAL_TOOL_RESULTS, <PERSON><PERSON><PERSON><PERSON>L_RESULT_LOCK
from config.settings import AppConfig
from config.attack_profiles import AttackProfile
from api.schemas.stream_events import (
    StreamEvent, EventTypes,
    AgentStartData, LLMTokenData, ToolCallStartData, ToolCallResultData,
    ReasoningStepData, AgentCompleteData, ErrorEventData,
    SearchResultData, KnowledgeDocumentData
)

# Configure logger for enhanced debugging
logger = logging.getLogger(__name__)

class StreamingAgentWorkflow:
    """Enhanced workflow that streams agent execution events."""
    
    def __init__(self, agent: Agent, config: AppConfig):
        self.agent = agent
        self.config = config
        self.attack_profile = config.get_attack_profile()
        self.session_id = str(uuid.uuid4())
        self.start_time = None
        
        # Tool tracking to prevent duplicates
        self._processed_tool_calls = set()
        self._completed_tool_calls = set()
        
    async def stream_agent_response(self, query: str) -> AsyncGenerator[StreamEvent, None]:
        """
        Stream agent execution with detailed event capture.
        
        Yields structured StreamEvent objects for real-time UI updates.
        """
        self.start_time = time.time()
        
        # Clear tool tracking for new query
        self._processed_tool_calls.clear()
        self._completed_tool_calls.clear()
        
        try:
            # Send agent start event
            yield StreamEvent(
                event=EventTypes.AGENT_START,
                data=AgentStartData(
                    attack_type=self.attack_profile.attack_type,
                    display_name=self.attack_profile.display_name,
                    knowledge_base=self.config.qdrant.collection_name,
                    specialized_terms=self.attack_profile.specialized_search_terms[:5],
                    session_id=self.session_id
                ).model_dump()
            )
            
            # Initialize response tracking
            final_answer = ""
            tools_used = []
            
            # Use native Agno streaming for rich event capture
            try:
                # Check if the agent has arun method for async execution
                if hasattr(self.agent, 'arun'):
                    # Use async run with streaming
                    agent_stream = await self.agent.arun(
                        query, 
                        stream=True, 
                        stream_intermediate_steps=True
                    )
                else:
                    # Use sync run method 
                    agent_stream = self.agent.run(
                        query, 
                        stream=True, 
                        stream_intermediate_steps=True
                    )
                
                # Process Agno events and convert to our streaming format
                async for agno_event in self._process_agno_events(agent_stream):
                    yield agno_event
                    
                    # Track final answer and tools
                    if agno_event.event == EventTypes.LLM_TOKEN:
                        final_answer += agno_event.data.get('token', '')
                    elif agno_event.event == EventTypes.TOOL_CALL_START:
                        tool_name = agno_event.data.get('tool_name')
                        if tool_name and tool_name not in tools_used:
                            tools_used.append(tool_name)
                
                # Get the final response from the agent
                if hasattr(self.agent, 'run_response') and self.agent.run_response:
                    final_answer = str(self.agent.run_response.content) if self.agent.run_response.content else ""
                
            except Exception as e:
                # Fallback to basic approach if native streaming fails
                response_future = asyncio.create_task(self._run_agent_and_capture(query))
                
                # Stream progress updates while waiting
                async for event in self._stream_with_wrapper(query, response_future):
                    yield event
                    # Track tools used
                    if event.event == EventTypes.TOOL_CALL_START:
                        tool_name = event.data.get('tool_name')
                        if tool_name and tool_name not in tools_used:
                            tools_used.append(tool_name)
                
                # Get the final response
                try:
                    final_answer = await response_future
                except Exception as e:
                    final_answer = f"Error: {str(e)}"
            
            # Send completion event
            total_time_ms = (time.time() - self.start_time) * 1000
            yield StreamEvent(
                event=EventTypes.AGENT_COMPLETE,
                data=AgentCompleteData(
                    final_answer=final_answer,
                    total_time_ms=total_time_ms,
                    tools_used=tools_used
                ).model_dump()
            )
            
        except Exception as e:
            # Send error event
            yield StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=str(e),
                    error_type=type(e).__name__,
                    code=500,
                    recoverable=False
                ).model_dump()
            )
    
    async def _stream_with_astream_log(self, query: str) -> AsyncGenerator[StreamEvent, None]:
        """Stream using Agno's astream_log method for detailed event capture."""
        
        try:
            # Use astream_log to get detailed execution steps
            async for chunk in self.agent.astream_log(
                {"input": query},
                include_types=["llm", "tool"],
                include_names=["final_answer"]
            ):
                
                for op in chunk.ops:
                    if op["op"] == "add":
                        path = op["path"]
                        value = op["value"]
                        
                        # Parse LLM token streams
                        if "/streamed_output" in path and isinstance(value, str) and value.strip():
                            yield StreamEvent(
                                event=EventTypes.LLM_TOKEN,
                                data=LLMTokenData(token=value).model_dump()
                            )
                        
                        # Parse tool calls
                        elif "/tool_calls/" in path and isinstance(value, dict):
                            tool_name = value.get("name", "unknown")
                            tool_input = value.get("args", {})
                            
                            yield StreamEvent(
                                event=EventTypes.TOOL_CALL_START,
                                data=ToolCallStartData(
                                    tool_name=tool_name,
                                    tool_input=tool_input,
                                    call_id=str(uuid.uuid4())
                                ).model_dump()
                            )
                        
                        # Parse tool results
                        elif "/tool_results/" in path and isinstance(value, dict):
                            await self._process_tool_result(value)
                        
                        # Parse reasoning steps
                        elif "/intermediate_steps" in path and isinstance(value, str):
                            if value.strip():
                                yield StreamEvent(
                                    event=EventTypes.REASONING_STEP,
                                    data=ReasoningStepData(
                                        step_type="thinking",
                                        message=value
                                    ).model_dump()
                                )
                                
        except Exception as e:
            yield StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=f"Streaming error: {str(e)}",
                    error_type=type(e).__name__
                ).model_dump()
            )
    
    async def _process_agno_events(self, agent_stream) -> AsyncGenerator[StreamEvent, None]:
        """Process native Agno RunResponseEvent objects and convert to our streaming format."""
        
        try:
            # Handle both sync and async generators
            if hasattr(agent_stream, '__aiter__'):
                # Async generator
                async for event in agent_stream:
                    async for stream_event in self._process_single_event(event):
                        yield stream_event
            else:
                # Sync generator/iterator
                for event in agent_stream:
                    async for stream_event in self._process_single_event(event):
                        yield stream_event
                        
        except Exception as e:
            yield StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=f"Event processing error: {str(e)}",
                    error_type=type(e).__name__
                ).model_dump()
            )
    
    async def _process_single_event(self, event) -> AsyncGenerator[StreamEvent, None]:
        """Process a single Agno event and yield corresponding stream events."""
        try:
            # Handle different Agno event types
            event_type = getattr(event, 'event', None)
            
            if event_type == "RunResponseContent":
                # LLM response content - stream as tokens
                if hasattr(event, 'content') and event.content:
                    yield StreamEvent(
                        event=EventTypes.LLM_TOKEN,
                        data=LLMTokenData(token=str(event.content)).model_dump()
                    )
            
            elif event_type == "ToolCallStarted":
                # Tool call with detailed information - Agno can start multiple tools at once
                # The event.tools contains an array of ToolExecution objects
                if hasattr(event, 'tools') and event.tools:
                    # Process only new tools we haven't seen before
                    for tool_exec in event.tools:
                        call_id = getattr(tool_exec, 'tool_call_id', str(uuid.uuid4()))
                        
                        # Skip if we've already processed this tool call
                        if call_id in self._processed_tool_calls:
                            continue
                            
                        # Mark as processed
                        self._processed_tool_calls.add(call_id)
                        
                        tool_name = getattr(tool_exec, 'tool_name', 'unknown')
                        tool_args = getattr(tool_exec, 'tool_args', {})
                        
                        # Create enhanced tool call event with search query information
                        yield StreamEvent(
                            event=EventTypes.TOOL_CALL_START,
                            data=ToolCallStartData(
                                tool_name=tool_name,
                                tool_input=tool_args,
                                call_id=call_id
                            ).model_dump()
                        )
                
                        # Add specific tool events based on tool type
                        if tool_name in ["think", "thinking"]:
                            # Handle thinking tool specifically
                            thinking_event = self._create_thinking_event(tool_args)
                            if thinking_event:
                                yield thinking_event
                        elif tool_name in ["analyze", "analysis"]:
                            # Handle analysis tool specifically
                            analysis_event = self._create_analysis_event(tool_args)
                            if analysis_event:
                                yield analysis_event
                        else:
                            # Handle search query information for other tools
                            search_event = self._create_search_query_event(tool_name, tool_args)
                            if search_event:
                                yield search_event
            
            elif event_type == "ToolCallCompleted":
                # Tool completion with results - handle multiple completed tools
                if hasattr(event, 'tools') and event.tools:
                    # Process only newly completed tools we haven't processed yet
                    for tool_exec in event.tools:
                        call_id = getattr(tool_exec, 'tool_call_id', str(uuid.uuid4()))
                        
                        # Skip if we've already processed this completion
                        if call_id in self._completed_tool_calls:
                            continue
                            
                        # Mark as completed
                        self._completed_tool_calls.add(call_id)
                        
                        tool_name = getattr(tool_exec, 'tool_name', 'unknown')
                        tool_result = getattr(tool_exec, 'result', None)
                        
                        yield await self._process_tool_completion(tool_name, tool_result, call_id)
            
            # Native reasoning events removed - using explicit think/analyze tools only
                
        except Exception as e:
            yield StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=f"Single event processing error: {str(e)}",
                    error_type=type(e).__name__
                ).model_dump()
            )
    
    def _create_search_query_event(self, tool_name: str, tool_args: dict) -> Optional[StreamEvent]:
        """Create specific search query events with detailed information."""
        
        if tool_name == "search_knowledge_base":
            query = tool_args.get('query', tool_args.get('search_query', 'Unknown query'))
            num_docs = tool_args.get('num_documents', tool_args.get('k', 5))
            
            return StreamEvent(
                event=EventTypes.REASONING_STEP,
                data=ReasoningStepData(
                    step_type="knowledge_search",
                    message=f"🔍 Searching knowledge base for: '{query}' (requesting {num_docs} documents)"
                ).model_dump()
            )
            
        elif tool_name == "web_search":
            query = tool_args.get('query', tool_args.get('search_query', 'Unknown query'))
            num_results = tool_args.get('num_results', tool_args.get('max_results', 10))
            
            return StreamEvent(
                event=EventTypes.REASONING_STEP,
                data=ReasoningStepData(
                    step_type="web_search",
                    message=f"🌐 Web searching for: '{query}' (requesting {num_results} results)"
                ).model_dump()
            )
        
        return None
    
    def _create_thinking_event(self, tool_args: dict) -> Optional[StreamEvent]:
        """Create thinking step event for the think tool."""
        
        thinking_content = tool_args.get('thought', tool_args.get('input', tool_args.get('content', '')))
        if not thinking_content:
            thinking_content = "Agent is thinking through the problem..."
        
        return StreamEvent(
            event=EventTypes.THINKING_STEP,
            data=ReasoningStepData(
                step_type="think_tool",
                message=f"💭 {thinking_content}",
                tool_data=tool_args
            ).model_dump()
        )
    
    def _create_analysis_event(self, tool_args: dict) -> Optional[StreamEvent]:
        """Create analysis step event for the analyze tool."""
        
        analysis_content = tool_args.get('analysis', tool_args.get('input', tool_args.get('content', '')))
        if not analysis_content:
            analysis_content = "Agent is analyzing previous results..."
        
        return StreamEvent(
            event=EventTypes.ANALYSIS_STEP,
            data=ReasoningStepData(
                step_type="analyze_tool",
                message=f"📊 {analysis_content}",
                tool_data=tool_args
            ).model_dump()
        )
    
    async def _process_tool_completion(self, tool_name: str, tool_content, call_id: str = None) -> StreamEvent:
        """Process tool completion with enhanced result validation and debugging."""
        
        search_results = None
        knowledge_documents = None
        result_summary = ""
        success = False
        error_message = None
        execution_time_ms = 0
        
        # Enhanced debugging: Log raw tool content
        logger.info(f"🔧 Processing tool completion: {tool_name} (call_id: {call_id})")
        logger.info(f"📊 Tool content type: {type(tool_content)}")
        logger.info(f"📋 Tool content: {str(tool_content)[:500]}...")
        
        # CRITICAL: Check if tool content is None and diagnose why
        if tool_content is None:
            logger.error(f"🚨 CRITICAL: {tool_name} returned None - attempting to retrieve from global registry")
            
            # Try to reconstruct the execution ID to find the result
            # This is a fallback and might not always be reliable
            # A more robust solution would be to pass execution_id through the context
            # NOTE: This reconstruction assumes we can get the original query object id, which we can't directly.
            # This part of the logic is complex because the context is lost.
            # For now, we iterate through the registry to find a plausible match.
            
            with GLOBAL_RESULT_LOCK:
                # Search for a result that matches the tool name and is not yet claimed
                # This is not perfect but can recover from some race conditions
                found_result = None
                best_match_key = None
                
                # This is a simplified matching for demonstration. A real implementation
                # would need a more robust way to link calls and results.
                for key, value in GLOBAL_TOOL_RESULTS.items():
                    if key.startswith(tool_name):
                        best_match_key = key
                        found_result = value
                        break # Take the first plausible match
                
                if found_result:
                    logger.info(f"✅ Found matching result in global registry for key: {best_match_key}")
                    tool_content = found_result
                    # To prevent re-using, we can remove it, though this has its own risks
                    # del GLOBAL_TOOL_RESULTS[best_match_key]
                else:
                    logger.error(f"   This suggests Agno called the tool but bypassed our synchronous wrapper")
                    logger.error(f"   Call ID: {call_id}")
                    logger.error(f"   This is likely a parallel tool execution issue in Agno")
        
        # CRITICAL: Also log repr() to see exact content structure
        logger.info(f"🔍 Tool content repr: {repr(tool_content)[:300]}...")
        
        # If it's a string, check if it might be a coroutine string representation or serialized JSON
        if isinstance(tool_content, str):
            logger.info(f"🧪 String content analysis:")
            logger.info(f"   - Starts with '[': {tool_content.startswith('[')}")
            logger.info(f"   - Starts with '{{': {tool_content.startswith('{')}")
            logger.info(f"   - Contains 'source': {'source' in tool_content}")
            logger.info(f"   - Contains 'coroutine object': {'coroutine object' in tool_content}")
            logger.info(f"   - First 100 chars: {tool_content[:100]}")
            
            # Check if this is a string representation of a coroutine object
            if 'coroutine object' in tool_content and 'search_knowledge_base' in tool_content:
                error_message = f"🚨 AGNO BUG: Tool returned string representation of coroutine - async tool was not properly awaited by framework"
                logger.error(f"❌ {tool_name} returned coroutine string: {tool_content[:100]}...")
                success = False
            elif 'coroutine object' in tool_content and 'web_search' in tool_content:
                error_message = f"🚨 AGNO BUG: Tool returned string representation of coroutine - async tool was not properly awaited by framework"
                logger.error(f"❌ {tool_name} returned coroutine string: {tool_content[:100]}...")
                success = False
            else:
                # Try to parse as JSON or Python literal if it looks like serialized data
                try:
                    import json
                    parsed_json = json.loads(tool_content)
                    logger.info(f"🎯 JSON parsing successful! Type: {type(parsed_json)}")
                    if isinstance(parsed_json, list):
                        logger.info(f"✅ JSON contains list with {len(parsed_json)} items")
                        # Replace tool_content with the parsed JSON
                        tool_content = parsed_json
                        logger.info(f"🔄 Updated tool_content to parsed JSON")
                except (json.JSONDecodeError, TypeError) as json_e:
                    logger.info(f"❌ JSON parsing failed: {json_e}")
                    
                    # Try parsing as Python literal (Agno might serialize with single quotes)
                    if tool_content.startswith('[') and tool_content.endswith(']'):
                        try:
                            import ast
                            parsed_literal = ast.literal_eval(tool_content)
                            logger.info(f"🎯 Python literal parsing successful! Type: {type(parsed_literal)}")
                            if isinstance(parsed_literal, list):
                                logger.info(f"✅ Python literal contains list with {len(parsed_literal)} items")
                                # Replace tool_content with the parsed literal
                                tool_content = parsed_literal
                                logger.info(f"🔄 Updated tool_content to parsed Python literal")
                        except (ValueError, SyntaxError) as ast_e:
                            logger.info(f"❌ Python literal parsing failed: {ast_e}")
        
        try:
            # 1. CRITICAL: Check for coroutine objects (async tool not awaited) and await them
            if inspect.iscoroutine(tool_content):
                logger.warning(f"🔧 {tool_name} returned coroutine - Agno failed to await it, doing manual await")
                try:
                    # Manually await the coroutine to get the actual result
                    tool_content = await tool_content
                    logger.info(f"✅ Successfully awaited {tool_name} coroutine, got: {type(tool_content)}")
                except Exception as await_e:
                    error_message = f"🚨 ASYNC ERROR: Failed to await tool coroutine: {str(await_e)}"
                    logger.error(f"❌ {tool_name} await failed: {await_e}")
                    success = False
                
            # 2. Check for None or empty results
            elif tool_content is None:
                error_message = f"Tool returned None - possible connection or configuration issue"
                logger.warning(f"⚠️  {tool_name} returned None")
                success = False
                
            # 3. Process web search results
            elif tool_name == "web_search":
                success, search_results, result_summary = self._process_web_search_results(tool_content)
                
            # 4. Process knowledge base results
            elif tool_name == "search_knowledge_base":
                success, knowledge_documents, result_summary = self._process_knowledge_search_results(tool_content)
                
            # 5. Process reasoning tool results (think, analyze)
            elif tool_name in ["think", "thinking", "analyze", "analysis"]:
                success, result_summary = self._process_reasoning_tool_results(tool_name, tool_content)
                
            else:
                # Unknown tool or unhandled case
                logger.warning(f"⚠️  Unknown tool type: {tool_name}")
                success = bool(tool_content)
                result_summary = f"Tool executed, returned: {type(tool_content).__name__}"
            
            # Log final processing result
            if success:
                logger.info(f"✅ {tool_name} processed successfully: {result_summary}")
            else:
                logger.error(f"❌ {tool_name} failed: {error_message or result_summary}")
            
            return StreamEvent(
                event=EventTypes.TOOL_CALL_RESULT,
                data=ToolCallResultData(
                    tool_name=tool_name,
                    call_id=call_id or str(uuid.uuid4()),
                    success=success,
                    error_message=error_message,
                    search_results=search_results,
                    knowledge_documents=knowledge_documents,
                    execution_time_ms=execution_time_ms,
                    result_summary=result_summary
                ).model_dump()
            )
            
        except Exception as e:
            error_msg = f"Exception processing {tool_name}: {str(e)}"
            logger.exception(f"💥 {error_msg}")
            
            return StreamEvent(
                event=EventTypes.TOOL_CALL_RESULT,
                data=ToolCallResultData(
                    tool_name=tool_name,
                    call_id=call_id or str(uuid.uuid4()),
                    success=False,
                    error_message=error_msg
                ).model_dump()
            )

    def _process_web_search_results(self, tool_content):
        """Process and validate web search results."""
        try:
            if not isinstance(tool_content, list):
                logger.warning(f"⚠️  Web search returned non-list: {type(tool_content)}")
                return False, None, f"❌ Expected list, got {type(tool_content).__name__}"
            
            if len(tool_content) == 0:
                logger.info("📊 Web search returned empty results")
                return True, [], "📊 No web results found for query"
            
            search_results = []
            valid_results = 0
            
            for i, result in enumerate(tool_content[:5]):  # Show top 5 results
                if isinstance(result, dict):
                    title = result.get("title", "No title")
                    url = result.get("link", result.get("source", ""))
                    snippet = result.get("snippet", result.get("content_preview", ""))
                    
                    if title and url:  # Validate we have meaningful data
                        search_results.append(SearchResultData(
                            title=title,
                            url=url,
                            snippet=snippet,
                            has_full_content=bool(result.get("content"))
                        ))
                        valid_results += 1
                    else:
                        logger.warning(f"⚠️  Skipping invalid web result {i}: missing title or URL")
                else:
                    logger.warning(f"⚠️  Skipping non-dict web result {i}: {type(result)}")
            
            result_summary = f"📊 Found {valid_results}/{len(tool_content)} valid web results"
            logger.info(f"✅ {result_summary}")
            
            return True, search_results, result_summary
            
        except Exception as e:
            logger.exception("💥 Error processing web search results")
            return False, None, f"❌ Error processing web results: {str(e)}"
    
    def _process_knowledge_search_results(self, tool_content):
        """Process and validate knowledge base search results."""
        try:
            if not isinstance(tool_content, list):
                logger.warning(f"⚠️  Knowledge search returned non-list: {type(tool_content)}")
                return False, None, f"❌ Expected list, got {type(tool_content).__name__}"
            
            if len(tool_content) == 0:
                logger.info("📚 Knowledge search returned empty results")
                return True, [], "📚 No knowledge documents found for query"
            
            knowledge_documents = []
            valid_docs = 0
            total_content_length = 0
            
            for i, doc in enumerate(tool_content[:5]):  # Show top 5 documents
                if isinstance(doc, dict):
                    source = doc.get("source", "Unknown")
                    content = doc.get("content", "")
                    content_preview = doc.get("content_preview", content)
                    
                    if content:  # Validate we have actual content
                        # Ensure preview is reasonable length
                        if len(content_preview) > 200:
                            content_preview = content_preview[:200] + "..."
                        
                        knowledge_documents.append(KnowledgeDocumentData(
                            source=source,
                            content_preview=content_preview,
                            metadata=doc.get("metadata", {}),
                            relevance_score=doc.get("relevance_score")
                        ))
                        valid_docs += 1
                        total_content_length += len(content)
                        
                        logger.info(f"📚 Doc {i+1}: {source[:50]}... ({len(content)} chars)")
                    else:
                        logger.warning(f"⚠️  Skipping empty knowledge doc {i}: {source}")
                else:
                    logger.warning(f"⚠️  Skipping non-dict knowledge result {i}: {type(doc)}")
            
            result_summary = f"📚 Found {valid_docs}/{len(tool_content)} valid documents ({total_content_length:,} chars total)"
            logger.info(f"✅ {result_summary}")
            
            return True, knowledge_documents, result_summary
            
        except Exception as e:
            logger.exception("💥 Error processing knowledge search results")
            return False, None, f"❌ Error processing knowledge results: {str(e)}"
    
    def _process_reasoning_tool_results(self, tool_name: str, tool_content):
        """Process and validate reasoning tool results (think, analyze)."""
        try:
            if tool_content is None:
                logger.info("🧠 Reasoning tool returned None - this is normal for thinking/analysis tools")
                return True, f"🧠 {tool_name.capitalize()} completed successfully"
            
            # Convert tool result to string if it's not already
            result_text = str(tool_content) if tool_content else ""
            
            # Clean up the result text
            if len(result_text) > 200:
                result_summary = result_text[:200] + "..."
            else:
                result_summary = result_text
            
            if result_text:
                logger.info(f"🧠 {tool_name} completed with result: {result_summary}")
                return True, f"🧠 {tool_name.capitalize()}: {result_summary}"
            else:
                logger.info(f"🧠 {tool_name} completed (internal processing)")
                return True, f"🧠 {tool_name.capitalize()} completed"
            
        except Exception as e:
            logger.exception(f"💥 Error processing {tool_name} results")
            return False, f"❌ Error processing {tool_name}: {str(e)}"

    async def _run_agent_and_capture(self, query: str) -> str:
        """Run the agent and capture its actual response."""
        try:
            # Use the agent's run method to get the actual response
            if hasattr(self.agent, 'arun'):
                response = await self.agent.arun(query)
            elif hasattr(self.agent, 'run'):
                response = self.agent.run(query)
            else:
                # Fallback - capture the printed output
                import io
                import sys
                from contextlib import redirect_stdout
                
                output_buffer = io.StringIO()
                with redirect_stdout(output_buffer):
                    await self.agent.aprint_response(query, stream=True)
                response = output_buffer.getvalue()
            
            return str(response) if response else ""
            
        except Exception as e:
            return f"Agent execution error: {str(e)}"
    
    async def _stream_with_wrapper(self, query: str, response_future: asyncio.Task) -> AsyncGenerator[StreamEvent, None]:
        """Stream progress updates while agent is running."""
        
        try:
            # Send reasoning steps while waiting for completion
            step_count = 0
            
            while not response_future.done():
                step_count += 1
                
                # Send thinking step
                yield StreamEvent(
                    event=EventTypes.REASONING_STEP,
                    data=ReasoningStepData(
                        step_type="thinking",
                        message=f"Processing query (step {step_count})..."
                    ).model_dump()
                )
                
                # Wait a bit before next update
                await asyncio.sleep(2.0)
                
                # Don't go beyond reasonable steps
                if step_count >= 10:
                    break
            
            # Check if we should yield a final thinking step
            if not response_future.done():
                yield StreamEvent(
                    event=EventTypes.REASONING_STEP,
                    data=ReasoningStepData(
                        step_type="finalizing",
                        message="Finalizing response..."
                    ).model_dump()
                )
                
        except Exception as e:
            yield StreamEvent(
                event=EventTypes.ERROR,
                data=ErrorEventData(
                    message=f"Wrapper streaming error: {str(e)}",
                    error_type=type(e).__name__
                ).model_dump()
            )
    
    async def _process_tool_result(self, tool_result: Dict[str, Any]) -> StreamEvent:
        """Process and structure tool results for UI display."""
        
        tool_name = tool_result.get("name", "unknown")
        result_data = tool_result.get("result", [])
        
        # Initialize result containers
        search_results = None
        knowledge_documents = None
        
        try:
            if tool_name == "web_search" and isinstance(result_data, list):
                # Process web search results
                search_results = []
                for result in result_data:
                    if isinstance(result, dict):
                        search_results.append(SearchResultData(
                            title=result.get("title", "No title"),
                            url=result.get("link", result.get("source", "")),
                            snippet=result.get("snippet", ""),
                            has_full_content=bool(result.get("content"))
                        ))
            
            elif tool_name == "search_knowledge_base" and isinstance(result_data, list):
                # Process knowledge base results
                knowledge_documents = []
                for doc in result_data:
                    if isinstance(doc, dict):
                        knowledge_documents.append(KnowledgeDocumentData(
                            source=doc.get("source", "Unknown"),
                            content_preview=doc.get("content_preview", ""),
                            metadata=doc.get("metadata", {}),
                            relevance_score=doc.get("relevance_score")
                        ))
            
            return StreamEvent(
                event=EventTypes.TOOL_CALL_RESULT,
                data=ToolCallResultData(
                    tool_name=tool_name,
                    success=True,
                    search_results=search_results,
                    knowledge_documents=knowledge_documents
                ).model_dump()
            )
            
        except Exception as e:
            return StreamEvent(
                event=EventTypes.TOOL_CALL_RESULT,
                data=ToolCallResultData(
                    tool_name=tool_name,
                    success=False,
                    error_message=str(e)
                ).model_dump()
            )