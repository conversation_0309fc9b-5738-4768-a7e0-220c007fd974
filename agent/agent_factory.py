from agno.agent import Agent
from agno.tools import tool
from config.settings import AppConfig, WebSearchConfig
from config.prompt_templates import AttackSpecificPrompts
from config.attack_profiles import AttackProfile
from tools.web_search import WebSearchTool
from tools.knowledge_search import KnowledgeSearchTool
from core.retriever import AsyncHybridRetriever
from agno.tools.thinking import ThinkingTools
from agno.tools.reasoning import ReasoningTools
import asyncio
import logging
import concurrent.futures
import threading

# Import tool execution systems for 100% reliable tool execution
from agent.tool_execution_proxy import (
    patch_agno_tool_execution, 
    register_tool_implementation,
    create_reliable_web_search_impl,
    create_reliable_knowledge_search_impl
)
from agent.tool_interceptor import ToolExecutionInterceptor

# Global semaphore to limit concurrent tool executions and prevent Agno race conditions
TOOL_EXECUTION_SEMAPHORE = threading.Semaphore(10)  # Allow max 10 concurrent tool calls for high-concurrency scenarios

class AgentFactory:
    """Factory for creating and configuring the SQL injection research agent."""
    
    @staticmethod
    def create_web_search_tool(web_search_config: WebSearchConfig):
        """Create web search tool function."""
        web_search_client = WebSearchTool({
            "search_backend": web_search_config.backend,
            "use_jina_reader": web_search_config.use_jina_reader,
            "timeout_seconds": web_search_config.timeout_seconds
        })
        
        @tool(
            name="web_search",
            description="Real-time web search tool using configured backend with timeout protection."
        )
        def web_search(query: str, num_results: int = 5) -> list[dict]:
            """Search the web for cybersecurity information and latest threats.
            
            Args:
                query: The search query string
                num_results: Number of search results to return (default: 5)
                
            Returns:
                List of dictionaries containing search results with title, link, snippet, and content
            """
            # WORKAROUND: Make synchronous to avoid Agno async tool bug
            # Use semaphore to limit concurrent executions and prevent race conditions
            logger = logging.getLogger(__name__)
            
            # Acquire semaphore to limit concurrent tool executions
            with TOOL_EXECUTION_SEMAPHORE:
                logger.info(f"🌐 [FACTORY CALLED] Web search called with query: '{query}', num_results: {num_results}")
                logger.info(f"🌐 [FACTORY CALLED] Thread: {threading.current_thread().name}, ID: {threading.get_ident()}")
                logger.info(f"🔒 [SEMAPHORE] Acquired tool execution lock ({TOOL_EXECUTION_SEMAPHORE._value} remaining)")
                
                try:
                    # More robust async execution that works in any threading context
                    def run_async_web_search():
                        """Run the async web search in a new event loop."""
                        try:
                            # Create a new event loop for this thread
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                result = loop.run_until_complete(web_search_client.asearch(query, num_results))
                                logger.info(f"✅ Web search completed successfully, got: {type(result)}")
                                if hasattr(result, '__len__'):
                                    logger.info(f"🌐 Web search returned {len(result)} results")
                                return result
                            finally:
                                loop.close()
                        except Exception as inner_e:
                            logger.error(f"❌ Inner async web search failed: {inner_e}")
                            raise inner_e
                    
                    # Try running in current thread first
                    try:
                        docs = asyncio.run(web_search_client.asearch(query, num_results))
                        logger.info(f"✅ Direct asyncio.run succeeded, got: {type(docs)}")
                    except RuntimeError as runtime_e:
                        if "cannot be called from a running event loop" in str(runtime_e):
                            logger.warning(f"⚠️ Event loop conflict, using thread executor: {runtime_e}")
                            # Use thread executor when event loop conflicts occur
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_async_web_search)
                                docs = future.result(timeout=30)  # 30 second timeout
                                logger.info(f"✅ Thread executor succeeded, got: {type(docs)}")
                        else:
                            raise runtime_e
                    
                    results = []
                    for doc in docs:
                        results.append({
                            "title": doc.metadata.get("title"),
                            "link": doc.metadata.get("source"),
                            "snippet": doc.metadata.get("snippet"),
                            "content": doc.page_content
                        })
                    logger.info(f"🔓 [SEMAPHORE] Releasing web search tool execution lock")
                    return results
                    
                except Exception as e:
                    logger.error(f"❌ Web search failed with exception: {type(e).__name__}: {e}")
                    import traceback
                    logger.error(f"📋 Full traceback: {traceback.format_exc()}")
                    logger.info(f"🔓 [SEMAPHORE] Releasing web search tool execution lock (with error)")
                    
                    return [{
                        "title": "Web Search Failed",
                        "link": "",
                        "snippet": f"Web search failed: {str(e)}",
                        "content": f"Web search failed: {str(e)}"
                    }]
        
        return web_search
    
    @staticmethod
    def create_knowledge_search_tool(retriever):
        """Create knowledge base search tool function."""
        if retriever is None:
            # Fallback function when knowledge base is not available
            @tool(
                name="search_knowledge_base",
                description="Search the cybersecurity knowledge base (fallback mode - knowledge base unavailable)."
            )
            async def fallback_search(query: str, num_documents: int = 5):
                """Search the cybersecurity knowledge base for relevant documents (fallback mode).
                
                Args:
                    query: The search query string
                    num_documents: Number of documents to return (default: 5)
                    
                Returns:
                    List of dictionaries containing fallback response indicating knowledge base unavailability
                """
                return [{
                    "source": "Knowledge base unavailable",
                    "title": "Fallback Mode",
                    "content": "Knowledge base connection failed. Agent running in web-search only mode.",
                    "content_preview": "Knowledge base connection failed. Agent running in web-search only mode.",
                    "metadata": {"fallback": True},
                    "relevance_score": 0.0
                }]
            return fallback_search
        
        knowledge_tool = KnowledgeSearchTool(retriever)
        
        @tool(
            name="search_knowledge_base",
            description="Search the cybersecurity knowledge base for relevant threat intelligence documents."
        )
        def search_knowledge_base(query: str, num_documents: int = 5) -> list[dict]:
            """Search the cybersecurity knowledge base for relevant documents and threat intelligence.
            
            Args:
                query: The search query string
                num_documents: Number of documents to return (default: 5)
                
            Returns:
                List of dictionaries containing knowledge base documents with source, content, metadata, and relevance scores
            """
            # WORKAROUND: Make synchronous to avoid Agno async tool bug
            # Use semaphore to limit concurrent executions and prevent race conditions
            logger = logging.getLogger(__name__)
            
            # Acquire semaphore to limit concurrent tool executions
            with TOOL_EXECUTION_SEMAPHORE:
                logger.info(f"🔍 [FACTORY CALLED] Knowledge base search called with query: '{query}', num_documents: {num_documents}")
                logger.info(f"🔍 [FACTORY CALLED] Thread: {threading.current_thread().name}, ID: {threading.get_ident()}")
                logger.info(f"🔒 [SEMAPHORE] Acquired knowledge tool execution lock ({TOOL_EXECUTION_SEMAPHORE._value} remaining)")
                
                try:
                    # More robust async execution that works in any threading context
                    def run_async_search():
                        """Run the async search in a new event loop."""
                        try:
                            # Create a new event loop for this thread
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                result = loop.run_until_complete(knowledge_tool.search_knowledge_base(query, num_documents))
                                logger.info(f"✅ Knowledge search completed successfully, got: {type(result)}")
                                if isinstance(result, list):
                                    logger.info(f"📚 Knowledge search returned {len(result)} documents")
                                return result
                            finally:
                                loop.close()
                        except Exception as inner_e:
                            logger.error(f"❌ Inner async search failed: {inner_e}")
                            raise inner_e
                    
                    # Try running in current thread first
                    try:
                        result = asyncio.run(knowledge_tool.search_knowledge_base(query, num_documents))
                        logger.info(f"✅ Direct asyncio.run succeeded, got: {type(result)}")
                        logger.info(f"🔓 [SEMAPHORE] Releasing knowledge tool execution lock")
                        return result
                    except RuntimeError as runtime_e:
                        if "cannot be called from a running event loop" in str(runtime_e):
                            logger.warning(f"⚠️ Event loop conflict, using thread executor: {runtime_e}")
                            # Use thread executor when event loop conflicts occur
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_async_search)
                                result = future.result(timeout=30)  # 30 second timeout
                                logger.info(f"✅ Thread executor succeeded, got: {type(result)}")
                                logger.info(f"🔓 [SEMAPHORE] Releasing knowledge tool execution lock")
                                return result
                        else:
                            raise runtime_e
                            
                except Exception as e:
                    logger.error(f"❌ Knowledge search failed with exception: {type(e).__name__}: {e}")
                    import traceback
                    logger.error(f"📋 Full traceback: {traceback.format_exc()}")
                    logger.info(f"🔓 [SEMAPHORE] Releasing knowledge tool execution lock (with error)")
                    
                    # Return error document instead of None
                    return [{
                        "source": "Knowledge Search Error",
                        "title": "Search Failed",
                        "content": f"Knowledge search encountered an error: {str(e)}",
                        "content_preview": f"Knowledge search failed: {str(e)}",
                        "metadata": {"error": True, "error_type": type(e).__name__},
                        "relevance_score": 0.0
                    }]
        
        return search_knowledge_base
    
    @staticmethod
    def create_agent(azure_model, retriever,
                     web_search_config: WebSearchConfig,
                     attack_profile: AttackProfile) -> Agent:
        """Create agent configured for specific attack type."""
        
        logger = logging.getLogger(__name__)
        
        # STEP 1: Initialize tool execution proxy for 100% reliable execution
        logger.info("🔧 [PROXY] Initializing tool execution proxy system...")
        
        # Patch Agno's Function.execute method to intercept all tool calls
        proxy_success = patch_agno_tool_execution()
        if proxy_success:
            logger.info("✅ [PROXY] Successfully patched Agno tool execution framework")
        else:
            logger.warning("⚠️ [PROXY] Failed to patch Agno - falling back to semaphore system")
        
        # STEP 2: Create tools with attack-specific timeouts
        web_search = AgentFactory.create_web_search_tool(web_search_config)
        search_knowledge_base = AgentFactory.create_knowledge_search_tool(retriever)
        
        # STEP 2.5: Create reasoning tools for agent thinking and analysis
        logger.info("🧠 [REASONING] Creating reasoning and thinking tools...")
        reasoning_tools = ReasoningTools(
            think=True,           # Enable thinking tool for step-by-step reasoning
            analyze=True,         # Enable analysis tool for evaluating results
            add_instructions=True, # Add reasoning instructions to agent
            add_few_shot=True     # Add few-shot examples for better reasoning
        )
        thinking_tools = ThinkingTools(
            add_instructions=True  # Add thinking instructions to agent
        )
        logger.info("✅ [REASONING] Reasoning tools created successfully")
        
        # STEP 3: Register reliable implementations in the proxy system
        if proxy_success:
            logger.info("🔧 [PROXY] Registering reliable tool implementations...")
            
            # Create and register reliable web search implementation
            web_search_client = WebSearchTool({
                "search_backend": web_search_config.backend,
                "use_jina_reader": web_search_config.use_jina_reader,
                "timeout_seconds": web_search_config.timeout_seconds
            })
            reliable_web_search = create_reliable_web_search_impl(web_search_client)
            register_tool_implementation("web_search", reliable_web_search)
            logger.info("✅ [PROXY] Registered reliable web_search implementation")
            
            # Create and register reliable knowledge search implementation if retriever available
            if retriever:
                knowledge_tool = KnowledgeSearchTool(retriever)
                reliable_knowledge_search = create_reliable_knowledge_search_impl(knowledge_tool)
                register_tool_implementation("search_knowledge_base", reliable_knowledge_search)
                logger.info("✅ [PROXY] Registered reliable search_knowledge_base implementation")
            else:
                logger.warning("⚠️ [PROXY] No retriever available - skipping knowledge search proxy registration")
        
        logger.info("🚀 [PROXY] Tool execution proxy system fully initialized")
        
        # STEP 4: Apply tool interceptor for 100% reliable execution
        interceptor = ToolExecutionInterceptor(web_search_config, retriever)
        
        # Generate dynamic prompts
        description = AttackSpecificPrompts.get_agent_description(
            attack_profile.attack_type,
            attack_profile.display_name
        )
        
        instructions = AttackSpecificPrompts.get_specialized_instructions(
            attack_profile.attack_type,
            attack_profile.research_phases
        )
        
        expected_output = AttackSpecificPrompts.get_attack_specific_output_template(
            attack_profile.attack_type,
            attack_profile.mitigation_categories,
            attack_profile.detection_categories
        )
        
        # Create agent with dynamic configuration and explicit think/analyze tools only
        logger.info("🚀 [AGENT] Creating agent with think and analyze tools...")
        agent = Agent(
            model=azure_model,
            tools=[web_search, search_knowledge_base, reasoning_tools, thinking_tools],
            show_tool_calls=True,
            debug_mode=False,
            description=description,
            instructions=[attack_profile.instructions, instructions], # Combine static and dynamic instructions
            expected_output=expected_output,
            markdown=True,
            add_datetime_to_instructions=True
        )
        logger.info("✅ [AGENT] Agent created with think and analyze tools")
        
        # STEP 5: Apply tool interceptor to ensure 100% reliable execution
        interceptor_success = interceptor.patch_agent_tools(agent)
        if interceptor_success:
            logger.info("✅ [INTERCEPTOR] Successfully applied tool interceptor to agent")
        else:
            logger.warning("⚠️ [INTERCEPTOR] Failed to apply tool interceptor - using fallback semaphore system")
        
        return agent