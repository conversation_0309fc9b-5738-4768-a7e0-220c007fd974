"""
Tool Interceptor - Ensures 100% reliable tool execution by intercepting at the agent level.
"""

import asyncio
import logging
import threading
import concurrent.futures
import uuid
from typing import Any, Dict, List
from tools.web_search import WebSearchTool
from tools.knowledge_search import KnowledgeSearchTool

logger = logging.getLogger(__name__)

# Global result registry accessible across all execution pathways
GLOBAL_TOOL_RESULTS = {}
GLOBAL_RESULT_LOCK = threading.RLock()

class ToolExecutionInterceptor:
    """Intercepts and ensures reliable execution of all tool calls."""
    
    def __init__(self, web_search_config, retriever):
        self.web_search_client = WebSearchTool({
            "search_backend": web_search_config.backend,
            "use_jina_reader": web_search_config.use_jina_reader,
            "timeout_seconds": web_search_config.timeout_seconds
        }) if web_search_config else None
        
        self.knowledge_tool = KnowledgeSearchTool(retriever) if retriever else None
        
        # Thread-safe execution tracking
        self.execution_lock = threading.RLock()
        self.active_executions = {}
        
        logger.info("🔧 [INTERCEPTOR] Tool execution interceptor initialized")
    
    def patch_agent_tools(self, agent):
        """Patch the agent's tools to use our reliable implementations."""
        logger.info("🔧 [INTERCEPTOR] Patching agent tools for 100% reliability")
        
        if not hasattr(agent, 'tools') or not agent.tools:
            logger.warning("⚠️ [INTERCEPTOR] Agent has no tools to patch")
            return False
        
        patched_count = 0
        
        for tool in agent.tools:
            if hasattr(tool, 'name'):
                tool_name = tool.name
                logger.info(f"🔧 [INTERCEPTOR] Examining tool: {tool_name}")
                
                if tool_name == "search_knowledge_base" and self.knowledge_tool:
                    # Replace the tool's entrypoint with our reliable implementation
                    original_entrypoint = tool.entrypoint
                    tool.entrypoint = self._create_reliable_knowledge_wrapper(original_entrypoint, tool_name)
                    patched_count += 1
                    logger.info(f"✅ [INTERCEPTOR] Patched knowledge search tool")
                
                elif tool_name == "web_search" and self.web_search_client:
                    # Replace the tool's entrypoint with our reliable implementation
                    original_entrypoint = tool.entrypoint
                    tool.entrypoint = self._create_reliable_web_wrapper(original_entrypoint, tool_name)
                    patched_count += 1
                    logger.info(f"✅ [INTERCEPTOR] Patched web search tool")
        
        logger.info(f"🚀 [INTERCEPTOR] Successfully patched {patched_count} tools")
        return patched_count > 0
    
    def _create_reliable_knowledge_wrapper(self, original_entrypoint, tool_name):
        """Create a reliable wrapper for knowledge search."""
        
        def reliable_knowledge_search(query: str, num_documents: int = 5) -> list[dict]:
            execution_id = f"{tool_name}_{uuid.uuid4()}"
            
            with self.execution_lock:
                if execution_id in self.active_executions:
                    logger.warning(f"⚠️ [INTERCEPTOR] Duplicate execution detected: {execution_id}")
                    return self.active_executions[execution_id]
                
                logger.info(f"🔍 [INTERCEPTOR] Reliable knowledge search: '{query}', docs: {num_documents}")
                logger.info(f"🔍 [INTERCEPTOR] Execution ID: {execution_id}")
                
                try:
                    # Mark as active
                    self.active_executions[execution_id] = "PROCESSING"
                    
                    # Execute with robust async handling
                    def run_knowledge_search(q, n_docs):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                result = loop.run_until_complete(
                                    self.knowledge_tool.search_knowledge_base(q, n_docs)
                                )
                                logger.info(f"✅ [INTERCEPTOR] Knowledge search completed: {type(result)}")
                                return result
                            finally:
                                loop.close()
                        except Exception as inner_e:
                            logger.error(f"❌ [INTERCEPTOR] Inner knowledge search failed: {inner_e}")
                            raise inner_e
                    
                    # Try direct execution first
                    try:
                        result = asyncio.run(
                            self.knowledge_tool.search_knowledge_base(query, num_documents)
                        )
                        logger.info(f"✅ [INTERCEPTOR] Direct knowledge search succeeded")
                    except RuntimeError as runtime_e:
                        if "cannot be called from a running event loop" in str(runtime_e):
                            logger.warning(f"⚠️ [INTERCEPTOR] Event loop conflict, using thread executor")
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_knowledge_search, query, num_documents)
                                result = future.result(timeout=30)
                                logger.info(f"✅ [INTERCEPTOR] Thread executor succeeded")
                        else:
                            raise runtime_e
                    
                    # Cache successful result
                    with GLOBAL_RESULT_LOCK:
                        GLOBAL_TOOL_RESULTS[execution_id] = result
                    self.active_executions[execution_id] = result
                    logger.info(f"🔓 [INTERCEPTOR] Knowledge search completed successfully")
                    return result
                    
                except Exception as e:
                    logger.error(f"❌ [INTERCEPTOR] Knowledge search failed: {type(e).__name__}: {e}")
                    
                    # Remove from active executions
                    self.active_executions.pop(execution_id, None)
                    
                    # Return error document instead of failing
                    error_result = [{
                        "source": "Knowledge Search Error (Interceptor)",
                        "title": "Search Failed",
                        "content": f"Knowledge search encountered an error: {str(e)}",
                        "content_preview": f"Knowledge search failed: {str(e)}",
                        "metadata": {"error": True, "error_type": type(e).__name__},
                        "relevance_score": 0.0
                    }]
                    with GLOBAL_RESULT_LOCK:
                        GLOBAL_TOOL_RESULTS[execution_id] = error_result
                    
                    self.active_executions[execution_id] = error_result
                    return error_result
        
        return reliable_knowledge_search
    
    def _create_reliable_web_wrapper(self, original_entrypoint, tool_name):
        """Create a reliable wrapper for web search."""
        
        def reliable_web_search(query: str, num_results: int = 5) -> list[dict]:
            execution_id = f"{tool_name}_{uuid.uuid4()}"
            
            with self.execution_lock:
                if execution_id in self.active_executions:
                    logger.warning(f"⚠️ [INTERCEPTOR] Duplicate execution detected: {execution_id}")
                    return self.active_executions[execution_id]
                
                logger.info(f"🌐 [INTERCEPTOR] Reliable web search: '{query}', results: {num_results}")
                logger.info(f"🌐 [INTERCEPTOR] Execution ID: {execution_id}")
                
                try:
                    # Mark as active
                    self.active_executions[execution_id] = "PROCESSING"
                    
                    # Execute with robust async handling
                    def run_web_search(q, n_results):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                result = loop.run_until_complete(
                                    self.web_search_client.asearch(q, n_results)
                                )
                                logger.info(f"✅ [INTERCEPTOR] Web search completed: {type(result)}")
                                return result
                            finally:
                                loop.close()
                        except Exception as inner_e:
                            logger.error(f"❌ [INTERCEPTOR] Inner web search failed: {inner_e}")
                            raise inner_e
                    
                    # Try direct execution first
                    try:
                        docs = asyncio.run(self.web_search_client.asearch(query, num_results))
                        logger.info(f"✅ [INTERCEPTOR] Direct web search succeeded")
                    except RuntimeError as runtime_e:
                        if "cannot be called from a running event loop" in str(runtime_e):
                            logger.warning(f"⚠️ [INTERCEPTOR] Event loop conflict, using thread executor")
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_web_search, query, num_results)
                                docs = future.result(timeout=30)
                                logger.info(f"✅ [INTERCEPTOR] Thread executor succeeded")
                        else:
                            raise runtime_e
                    
                    # Convert to expected format
                    results = []
                    for doc in docs:
                        results.append({
                            "title": doc.metadata.get("title"),
                            "link": doc.metadata.get("source"),
                            "snippet": doc.metadata.get("snippet"),
                            "content": doc.page_content
                        })
                    
                    # Cache successful result
                    with GLOBAL_RESULT_LOCK:
                        GLOBAL_TOOL_RESULTS[execution_id] = results
                    self.active_executions[execution_id] = results
                    logger.info(f"🔓 [INTERCEPTOR] Web search completed successfully")
                    return results
                    
                except Exception as e:
                    logger.error(f"❌ [INTERCEPTOR] Web search failed: {type(e).__name__}: {e}")
                    
                    # Remove from active executions
                    self.active_executions.pop(execution_id, None)
                    
                    # Return error result instead of failing
                    error_result = [{
                        "title": "Web Search Failed (Interceptor)",
                        "link": "",
                        "snippet": f"Web search failed: {str(e)}",
                        "content": f"Web search failed: {str(e)}"
                    }]
                    
                    with GLOBAL_RESULT_LOCK:
                        GLOBAL_TOOL_RESULTS[execution_id] = error_result
                    self.active_executions[execution_id] = error_result
                    return error_result
        
        return reliable_web_search