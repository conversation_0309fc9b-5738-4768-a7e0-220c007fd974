#!/usr/bin/env python3
"""
Test script to diagnose Qdrant connection issues.
"""

import os
import asyncio
import time
from dotenv import load_dotenv
from qdrant_client import QdrantClient

load_dotenv()

async def test_qdrant_connection():
    """Test Qdrant connection with detailed debugging."""
    
    print("🔍 Testing Qdrant Connection")
    print("=" * 50)
    
    # Get configuration from environment
    qdrant_url = os.getenv("QDRANT_URL")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    collection_name = os.getenv("COLLECTION_NAME", "sql_injection")
    
    print(f"URL: {qdrant_url}")
    print(f"API Key: {qdrant_api_key[:10]}..." if qdrant_api_key else "No API Key")
    print(f"Collection: {collection_name}")
    print()
    
    if not qdrant_url or not qdrant_api_key:
        print("❌ Missing Qdrant configuration!")
        return
    
    try:
        print("🔗 Creating Qdrant client...")
        start_time = time.time()
        
        client = QdrantClient(
            url=qdrant_url,
            api_key=qdrant_api_key,
            timeout=30.0
        )
        
        creation_time = time.time() - start_time
        print(f"✅ Client created in {creation_time:.2f}s")
        
        print("📋 Getting collections...")
        start_time = time.time()
        
        collections = client.get_collections()
        
        fetch_time = time.time() - start_time
        print(f"✅ Collections fetched in {fetch_time:.2f}s")
        
        print(f"📊 Found {len(collections.collections)} collections:")
        for col in collections.collections:
            print(f"   - {col.name}")
        
        # Check if target collection exists
        collection_names = [col.name for col in collections.collections]
        if collection_name in collection_names:
            print(f"✅ Target collection '{collection_name}' found!")
            
            # Get collection info
            print(f"📈 Getting collection info for '{collection_name}'...")
            start_time = time.time()
            
            collection_info = client.get_collection(collection_name)
            
            info_time = time.time() - start_time
            print(f"✅ Collection info retrieved in {info_time:.2f}s")
            print(f"   - Vectors: {collection_info.vectors_count}")
            print(f"   - Points: {collection_info.points_count}")
            
        else:
            print(f"⚠️  Target collection '{collection_name}' not found!")
            print(f"Available collections: {collection_names}")
        
        print()
        print("🎉 Qdrant connection test completed successfully!")
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_qdrant_connection())