# Python
**/__pycache__
*.py[cod]
*./*.pyc
__pycache__/
*.py[cod]
*$py.class
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Environment variables and secrets
.env
.env.local
.env.development
.env.test
.env.production
*.env
config.ini
secrets.json
credentials.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Logs
*.log
logs/
log/

# Database files
*.db
*.sqlite
*.sqlite3

# Vector database files
qdrant_data/
chroma_db/
faiss_index/

# Model files and artifacts
models/
*.pkl
*.pickle
*.joblib
*.h5
*.pb
*.onnx
*.bin

# Data files
data/
datasets/
*.csv
*.json
*.xml
*.parquet
!requirements.txt
!package.json
!config.json

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store?
ehthumbs.db
Icon?

# Documentation build
docs/_build/
site/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Project specific
# Keep CLAUDE.md but ignore local overrides
CLAUDE.local.md
todo
repomix-output.xml

# API keys and credentials (additional protection)
*key*
*secret*
*token*
*password*
*credentials*
api_keys.py
secrets.py

# SSL certificates
*.pem
*.crt
*.key
*.p12
*.pfx

# Backup files
*.bak
*.backup
*~