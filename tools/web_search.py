import os
import asyncio
import warnings
from dotenv import load_dotenv
from typing import List, Dict, Any, Optional
from langchain.schema import Document

# Import necessary libraries for each search backend
from azure.cognitiveservices.search.websearch import WebSearchClient
from msrest.authentication import CognitiveServicesCredentials
from googleapiclient.discovery import build
# Try importing GoogleSearch from the correct package
try:
    from serpapi import GoogleSearch
except ImportError:
    try:
        from google_search_results import GoogleSearch
    except ImportError:
        raise ImportError(
            "Could not import GoogleSearch. Please install the official SerpAPI package with: "
            "'pip install google-search-results'."
        )
# Only import JinaClient if needed (see constructor)

load_dotenv()

class WebSearchTool:
    def __init__(self, config: Dict[str, Any]):
        self.search_backend = config.get("search_backend", "bing")
        self.use_jina_reader = config.get("use_jina_reader", False)
        self.timeout_seconds = config.get("timeout_seconds", 10)
        self.jina_client = None
        if self.use_jina_reader:
            try:
                from jina import JinaClient
                self.jina_client = JinaClient(
                    host="https://api.jina.ai",
                    headers={"Authorization": f"Bearer {os.getenv('JINA_API_KEY')}"}
                )
            except ImportError:
                print("Warning: Jina not installed. Full-page content extraction disabled.")
                print("To enable: pip install jina")
                self.use_jina_reader = False

        if self.search_backend == "bing":
            self.bing_endpoint = os.getenv("AZURE_BING_SEARCH_ENDPOINT")
            self.bing_key = os.getenv("AZURE_BING_SEARCH_KEY")
            if not self.bing_endpoint or not self.bing_key:
                raise ValueError("AZURE_BING_SEARCH_ENDPOINT and AZURE_BING_SEARCH_KEY must be set for Bing backend.")
            self.bing_client = WebSearchClient(self.bing_endpoint, CognitiveServicesCredentials(self.bing_key))
        elif self.search_backend == "google":
            self.google_api_key = os.getenv("GOOGLE_CSE_API_KEY")
            self.google_cse_cx = os.getenv("GOOGLE_CSE_CX")
            if not self.google_api_key or not self.google_cse_cx:
                raise ValueError("GOOGLE_CSE_API_KEY and GOOGLE_CSE_CX must be set for Google backend.")
            
            # The library should work correctly without SSL monkey-patching if the environment is configured properly
            self.google_service = build("customsearch", "v1", developerKey=self.google_api_key)
        elif self.search_backend == "serpapi":
            self.serpapi_key = os.getenv("SERPAPI_KEY")
            if not self.serpapi_key:
                raise ValueError("SERPAPI_KEY must be set for SerpAPI backend.")
        else:
            raise ValueError(f"Unsupported search backend: {self.search_backend}")

    def _search_bing(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        try:
            web_data = self.bing_client.web.search(query=query, count=num_results)
            results = []
            if hasattr(web_data, 'web_pages') and web_data.web_pages.value:
                for page in web_data.web_pages.value:
                    results.append({
                        "title": page.name,
                        "link": page.url,
                        "snippet": page.snippet
                    })
            return results
        except Exception as e:
            raise ConnectionError(f"Bing search failed: {e}") from e

    def _search_google(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        try:
            res = self.google_service.cse().list(q=query, cx=self.google_cse_cx, num=num_results).execute()
            results = []
            if 'items' in res:
                for item in res['items']:
                    results.append({
                        "title": item.get('title'),
                        "link": item.get('link'),
                        "snippet": item.get('snippet')
                    })
            return results
        except Exception as e:
            raise ConnectionError(f"Google Custom Search failed: {e}") from e

    def _search_serpapi(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        try:
            params = {
                "engine": "google", # SerpAPI can use different engines, default to google
                "q": query,
                "api_key": self.serpapi_key,
                "num": num_results
            }
            search = GoogleSearch(params)
            res = search.get_dict()
            results = []
            if 'organic_results' in res:
                for item in res['organic_results']:
                    results.append({
                        "title": item.get('title'),
                        "link": item.get('link'),
                        "snippet": item.get('snippet')
                    })
            return results
        except Exception as e:
            raise ConnectionError(f"SerpAPI search failed: {e}") from e

    def _fetch_with_jina(self, url: str) -> Optional[str]:
        try:
            if self.jina_client:
                response = self.jina_client.post(
                    "/read",
                    data={"url": url},
                    headers={"X-Return-Format": "text"}
                )
                if response.status_code == 200:
                    return response.text
            return None
        except Exception as e:
            # For Jina Reader, we can gracefully degrade to snippet content
            print(f"Warning: Jina Reader failed for {url}: {e}")
            return None

    def search(self, query: str, num_results: int = 5) -> List[Document]:
        search_results = []
        if self.search_backend == "bing":
            search_results = self._search_bing(query, num_results)
        elif self.search_backend == "google":
            search_results = self._search_google(query, num_results)
        elif self.search_backend == "serpapi":
            search_results = self._search_serpapi(query, num_results)

        documents = []
        for res in search_results:
            page_content = res.get("snippet", "")
            if self.use_jina_reader and res.get("link"):
                full_content = self._fetch_with_jina(res["link"])
                if full_content:
                    page_content = full_content # Use full content if available

            documents.append(Document(
                page_content=page_content,
                metadata={
                    "source": res.get("link"),
                    "title": res.get("title"),
                    "snippet": res.get("snippet") # Keep snippet in metadata
                }
            ))
        return documents
    
    async def asearch(self, query: str, num_results: int = 5) -> List[Document]:
        """Async search with timeout handling to prevent hanging requests."""
        try:
            # Wrap the synchronous search in asyncio.to_thread with timeout
            documents = await asyncio.wait_for(
                asyncio.to_thread(self.search, query, num_results),
                timeout=self.timeout_seconds
            )
            return documents
        except asyncio.TimeoutError:
            # Log warning and return empty list on timeout
            print(f"Warning: Web search for query '{query}' timed out after {self.timeout_seconds} seconds.")
            return []
        except Exception as e:
            # Log error and re-raise for proper error handling
            print(f"Error during async web search for query '{query}': {e}")
            raise ConnectionError(f"Async web search failed: {e}") from e

if __name__ == "__main__":
    # Example Usage (for testing purposes)
    # Ensure .env has necessary API keys
    # For Bing: AZURE_BING_SEARCH_ENDPOINT, AZURE_BING_SEARCH_KEY
    # For Google: GOOGLE_CSE_API_KEY, GOOGLE_CSE_CX
    # For SerpAPI: SERPAPI_KEY
    # For Jina: JINA_API_KEY

    print("Testing Bing Search with Jina Reader...")
    bing_config = {"search_backend": "bing", "use_jina_reader": True}
    bing_search_tool = WebSearchTool(bing_config)
    bing_docs = bing_search_tool.search("latest SQL injection vulnerabilities 2024", num_results=2)
    for doc in bing_docs:
        print(f"Title: {doc.metadata.get('title')}\nSource: {doc.metadata.get('source')}\nContent (first 200 chars): {doc.page_content[:200]}...\n---")

    print("\nTesting Google Search without Jina Reader...")
    google_config = {"search_backend": "google", "use_jina_reader": False}
    google_search_tool = WebSearchTool(google_config)
    google_docs = google_search_tool.search("SQLi bypass techniques", num_results=2)
    for doc in google_docs:
        print(f"Title: {doc.metadata.get('title')}\nSource: {doc.metadata.get('source')}\nContent (snippet): {doc.page_content}\n---")

    print("\nTesting SerpAPI Search with Jina Reader...")
    serpapi_config = {"search_backend": "serpapi", "use_jina_reader": True}
    serpapi_search_tool = WebSearchTool(serpapi_config)
    serpapi_docs = serpapi_search_tool.search("SQL injection exploit details", num_results=2)
    for doc in serpapi_docs:
        print(f"Title: {doc.metadata.get('title')}\nSource: {doc.metadata.get('source')}\nContent (first 200 chars): {doc.page_content[:200]}...\n---")