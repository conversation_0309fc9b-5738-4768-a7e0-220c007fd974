from typing import List, Dict
from core.retriever import AsyncHybridRetriever

class KnowledgeSearchTool:
    """Tool for searching the cybersecurity knowledge base."""
    
    def __init__(self, retriever: AsyncHybridRetriever):
        self.retriever = retriever
    
    async def search_knowledge_base(self, query: str, num_documents: int = 5) -> List[Dict]:
        """Search the cybersecurity knowledge base for relevant documents.
        
        Returns structured data optimized for streaming UI display.
        """
        # Call the public async method of the retriever
        docs = await self.retriever.aget_relevant_documents(query)
        
        structured_results = []
        for doc in docs[:num_documents]:
            # Create content preview (first 200 chars for UI display)
            content = doc.page_content
            content_preview = content[:200] + "..." if len(content) > 200 else content
            
            # Extract source information from metadata
            source = doc.metadata.get("source", "Unknown")
            title = doc.metadata.get("title", source.split("/")[-1] if "/" in source else "Document")
            
            structured_results.append({
                "source": source,
                "title": title,
                "content": content,  # Full content for processing
                "content_preview": content_preview,  # Short preview for UI
                "metadata": doc.metadata,
                "relevance_score": getattr(doc, 'relevance_score', None)
            })
        
        return structured_results