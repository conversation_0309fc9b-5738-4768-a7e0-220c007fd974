#!/usr/bin/env python3
"""
Debug monitor for knowledge retrieval issues.
Sends test queries and monitors server response for debugging.
"""

import asyncio
import aiohttp
import json

async def send_test_query():
    """Send a test query to trigger knowledge base search."""
    
    url = "http://localhost:8000/api/v1/chat/stream"
    data = {
        "query": "test specific knowledge: cassandra example #2 or Oracle injection techniques", 
        "attack_type": "sqli"
    }
    
    print(f"🧪 Sending test query: {data['query']}")
    print("=" * 80)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    print(f"❌ Error: HTTP {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
                    return
                
                print("📡 Monitoring streaming response for debugging info...")
                print("-" * 80)
                
                current_event = None
                current_data = None
                tool_calls = []
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('event: '):
                        current_event = line[7:].strip()
                    elif line.startswith('data: '):
                        try:
                            current_data = json.loads(line[6:])
                        except json.JSONDecodeError as e:
                            print(f"⚠️  JSON decode error: {e}")
                            continue
                    elif line.strip() == '' and current_event and current_data:
                        # Process complete event
                        if current_event == 'tool_call_result':
                            tool_name = current_data.get('tool_name', 'unknown')
                            success = current_data.get('success', False)
                            error_message = current_data.get('error_message', '')
                            result_summary = current_data.get('result_summary', '')
                            
                            print(f"🔧 TOOL RESULT: {tool_name}")
                            print(f"   ✅ Success: {success}")
                            if error_message:
                                print(f"   ❌ Error: {error_message}")
                            if result_summary:
                                print(f"   📊 Summary: {result_summary}")
                            
                            # Check for knowledge documents
                            if current_data.get('knowledge_documents'):
                                docs = current_data['knowledge_documents']
                                print(f"   📚 Knowledge docs found: {len(docs)}")
                                for i, doc in enumerate(docs[:2]):
                                    source = doc.get('source', 'Unknown')[:50]
                                    preview = doc.get('content_preview', '')[:100]
                                    print(f"      {i+1}. {source}... | {preview}...")
                            
                            # Check for search results  
                            if current_data.get('search_results'):
                                results = current_data['search_results']
                                print(f"   🌐 Web results found: {len(results)}")
                                for i, result in enumerate(results[:2]):
                                    title = result.get('title', 'No title')[:50]
                                    url = result.get('url', 'No URL')[:50]
                                    print(f"      {i+1}. {title}... | {url}...")
                            
                            print()
                            tool_calls.append({
                                'tool': tool_name,
                                'success': success,
                                'error': error_message,
                                'summary': result_summary
                            })
                        
                        elif current_event == 'agent_complete':
                            print("🏁 Agent completed!")
                            break
                        
                        # Reset for next event
                        current_event = None
                        current_data = None
                
                print("=" * 80)
                print(f"📊 SUMMARY: {len(tool_calls)} tool calls processed")
                
                failed_tools = [t for t in tool_calls if not t['success']]
                if failed_tools:
                    print(f"❌ Failed tools: {len(failed_tools)}")
                    for tool in failed_tools:
                        print(f"   - {tool['tool']}: {tool['error'] or tool['summary']}")
                else:
                    print("✅ All tools succeeded")
                    
    except Exception as e:
        print(f"❌ Request error: {e}")

if __name__ == "__main__":
    asyncio.run(send_test_query())