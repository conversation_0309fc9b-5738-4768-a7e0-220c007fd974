# Attack Specialization Configuration
ATTACK_PROFILE=sqli  # Options: sqli, xss, rce, lfi, etc.

# Qdrant Configuration (will auto-set collection based on attack type if not specified)
QDRANT_URL=your_qdrant_url
QDRANT_API_KEY=your_qdrant_api_key
COLLECTION_NAME=  # Leave empty for auto-assignment based on attack profile

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_CHAT_DEPLOYMENT=your_chat_deployment_name
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=your_embedding_deployment_name

# Web Search Configuration
WEB_SEARCH_BACKEND=bing  # Options: "bing", "google", "serpapi"
USE_JINA_READER=false    # Options: "true", "false"

# Web Search API Keys (configure based on WEB_SEARCH_BACKEND)
# For Bing Search
AZURE_BING_SEARCH_ENDPOINT=your_bing_search_endpoint
AZURE_BING_SEARCH_KEY=your_bing_search_key

# For Google Custom Search
GOOGLE_CSE_API_KEY=your_google_cse_api_key
GOOGLE_CSE_CX=your_google_cse_cx

# For SerpAPI
SERPAPI_KEY=your_serpapi_key

# For Jina Reader (if USE_JINA_READER=true)
JINA_API_KEY=your_jina_api_key

# Azure ML Reranking Configuration
AZURE_ML_RERANK_ENDPOINT=https://api-kiizi.swedencentral.inference.ml.azure.com/score
AZURE_ML_RERANK_KEY=Axx6Q7Ylctn53I9Rm1eLamsFSybCHGYEFlvLsk34sAgppp1yNX9TJQQJ99BFAAAAAAAAAAAAINFRAZMLRCRd