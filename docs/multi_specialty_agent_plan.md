## Detailed Implementation Plan for Multi-Specialty Threat Intelligence Agents

### **Overall Goal**
To transform the current single-specialty SQL Injection threat intelligence agent into a multi-specialty agent capable of handling various attack types (XSS, RCE, SQLi, LFI, etc.) through configurable profiles and dynamic prompting, incorporating robustness, testing, and operational considerations.

### **Architectural Overview**

The new architecture introduces a clear separation of concerns, allowing different attack specializations to be defined and managed independently.

```mermaid
graph TD
    A[CLI / Environment Variable] --> B{sqli_agent.py};
    B --> C[AppConfig.from_env(attack_type)];
    C --> D{config/attack_profiles.py};
    D -- Retrieves --> E[AttackProfile (e.g., XSS_PROFILE)];
    C -- Uses --> E;
    B --> F[ModelFactory];
    B --> G[KnowledgeBaseFactory];
    G --> H[AsyncHybridRetriever];
    B --> I[AgentFactory.create_agent(..., attack_profile)];
    I --> J[AttackSpecificPrompts (from config/prompt_templates.py)];
    J -- Generates --> K[Dynamic Prompts];
    I --> L[WebSearchTool];
    I --> M[KnowledgeSearchTool];
    I -- Creates --> N[Agent (with dynamic prompts and tools)];
    B --> O[AgentWorkflow(agent, config)];
    O -- Runs --> P[Interactive Session];
    P -- Uses --> N;
    N -- Interacts with --> L;
    N -- Interacts with --> M;
    M -- Interacts with --> H;
```

### **Implementation Steps**

1.  **Create `config/attack_profiles.py`**:
    *   Define the `AttackProfile` dataclass.
    *   **Enhancement**: Add a `validate` method to `AttackProfile` to check for required fields.
        ```python
        def validate(self) -> bool:
            """Validate profile configuration completeness."""
            required_fields = [
                'attack_type', 'display_name', 'qdrant_collection',
                'description', 'instructions'
            ]
            # Ensure research_phases is not empty if present, etc.
            return all(getattr(self, field) for field in required_fields) and \
                   (self.research_phases is not None and len(self.research_phases) > 0) and \
                   (self.specialized_search_terms is not None and len(self.specialized_search_terms) > 0) and \
                   (self.mitigation_categories is not None and len(self.mitigation_categories) > 0) and \
                   (self.detection_categories is not None and len(self.detection_categories) > 0)
        ```
    *   **Enhancement**: Add `__post_init__` to `AttackProfile` for immediate validation.
        ```python
        def __post_init__(self):
            """Validate profile after initialization."""
            if not self.validate():
                raise ValueError(f"Invalid profile configuration for {self.attack_type}")
        ```
    *   Define the `ATTACK_PROFILES` dictionary.
    *   **Enhancement**: Include explicit imports for profiles from `config/profiles/` and assign them to `ATTACK_PROFILES`:
        ```python
        from config.profiles.xss_profile import XSS_PROFILE
        from config.profiles.rce_profile import RCE_PROFILE
        # from config.profiles.sqli_profile import SQLI_PROFILE # Will be created later
        # from config.profiles.lfi_profile import LFI_PROFILE # Will be created later

        ATTACK_PROFILES = {
            "xss": XSS_PROFILE,
            "rce": RCE_PROFILE,
            # "sqli": SQLI_PROFILE,
            # "lfi": LFI_PROFILE,
        }
        ```

2.  **Create `config/prompt_templates.py`**:
    *   Define the `AttackSpecificPrompts` class.
    *   Implement the static methods `get_agent_description`, `get_specialized_instructions`, and `get_attack_specific_output_template` as specified in the plan.

3.  **Create `config/profiles/` directory and attack-specific profiles**:
    *   Create the `config/profiles/` directory.
    *   Create [`config/profiles/xss_profile.py`](config/profiles/xss_profile.py) and define the `XSS_PROFILE` instance of `AttackProfile`.
    *   Create [`config/profiles/rce_profile.py`](config/profiles/rce_profile.py) and define the `RCE_PROFILE` instance of `AttackProfile`.
    *   (Future steps: Add `sqli_profile.py`, `lfi_profile.py`, etc., and import them into `config/attack_profiles.py`.)

4.  **Modify `config/settings.py`**:
    *   Import `os` and `ATTACK_PROFILES` from `config/attack_profiles.py`.
    *   Add `attack_profile: str = "sqli"` and `auto_collection_name: bool = True` to the `AppConfig` dataclass.
    *   Update the `from_env` class method to:
        *   Accept an optional `attack_type` parameter.
        *   Load the `ATTACK_PROFILE` environment variable or use "sqli" as default.
        *   **Enhancement**: Add comprehensive validation for environment variables.
        *   Validate that the `attack_profile` exists in `ATTACK_PROFILES`.
        *   Set `config.qdrant.collection_name` based on the selected `AttackProfile` if `auto_collection_name` is `True`.
        *   **Enhancement**: Add validation to ensure the Qdrant collection name is set, either manually or via auto-assignment.
        *   **Enhancement**: Add graceful fallbacks for missing profile configurations (e.g., sensible defaults or specific error messages).
    *   Add the `get_attack_profile` method to `AppConfig` to retrieve the current `AttackProfile` instance.

5.  **Refactor `config/prompts.py`**:
    *   **Action**: Remove `config/prompts.py` entirely.
    *   Ensure `config/prompt_templates.py` is created with the dynamic `AttackSpecificPrompts` class.
    *   Update imports in `agent/agent_factory.py` to use `config.prompt_templates.AttackSpecificPrompts`.

6.  **Modify `agent/agent_factory.py`**:
    *   Import `AttackProfile` from `config/attack_profiles.py` and `AttackSpecificPrompts` from `config/prompt_templates.py`.
    *   Update the `create_agent` static method to accept an `attack_profile: AttackProfile` parameter.
    *   Modify the agent initialization to use `AttackSpecificPrompts` to generate the `description`, `instructions`, and `expected_output` dynamically based on the provided `attack_profile`.

7.  **Modify `agent/workflow.py`**:
    *   Import `ATTACK_PROFILES` from `config/attack_profiles.py`.
    *   In the `__init__` method, store the `attack_profile` by calling `config.get_attack_profile()`.
    *   Update the `run_interactive_session` method to:
        *   Print the `display_name` and `attack_type` from the `attack_profile`.
        *   Add logic to handle the "switch <attack_type>" command (as noted in the plan, this will initially only print a message about future implementation).
        *   **Enhancement**: Add validation that the Qdrant collection exists for the selected attack type before starting the session.

8.  **Modify `sqli_agent.py`**:
    *   Import `argparse` and `ATTACK_PROFILES` from `config/attack_profiles.py`.
    *   Update the `run_agent_async` function to accept an optional `attack_type` parameter, which will be passed to `AppConfig.from_env()`.
    *   Modify the agent creation to pass the `attack_profile` to `AgentFactory.create_agent()`.
    *   Implement the `main` function to use `argparse` for:
        *   An `--attack-type` or `-a` argument, with choices from `ATTACK_PROFILES.keys()`.
        *   A `--list-attacks` or `-l` action to print available attack specializations.
        *   **Enhancement**: Add `--validate-profile` or `-v` argument to validate a specific attack profile configuration.
        *   **Enhancement**: Add `--collection-check` or `-c` argument to check if Qdrant collections exist for all profiles.
    *   Ensure the `attack_type` is determined from CLI arguments or the `ATTACK_PROFILE` environment variable.

9.  **Update `.env.example`**:
    *   Add a new section for "Attack Specialization Configuration" with `ATTACK_PROFILE=sqli` and a comment explaining the options.
    *   Modify the `COLLECTION_NAME` comment to indicate it can be left empty for auto-assignment.

### **Recommended Additions (for Future Iterations/Completeness)**

10. **Add Profile Validation & Error Handling**:
    *   Implement robust validation methods for `AttackProfile` (as added in step 1).
    *   Add error handling for missing/invalid configurations in `AppConfig.from_env` and `AgentFactory.create_agent`.
    *   Create fallback mechanisms for incomplete profiles, logging warnings or using default values where appropriate.

11. **Create Migration Utilities**:
    *   Develop a script to check existing Qdrant collections and report their status.
    *   Create a utility to validate all defined attack profiles programmatically.
    *   Consider a helper to migrate from the old single-collection setup to the new multi-collection approach, if existing data needs to be preserved and moved.

12. **Add Comprehensive Testing**:
    *   Write unit tests for `AttackProfile` validation.
    *   Develop unit tests for `AttackSpecificPrompts` to ensure dynamic prompt generation works correctly.
    *   Implement integration tests for the entire agent workflow, verifying that the correct attack profile is loaded and applied.
    *   Create tests for CLI argument parsing and environment variable configuration.

13. **Update Documentation**:
    *   Revise `CLAUDE.md` to reflect the new multi-attack capabilities, usage instructions, and examples.
    *   Add a dedicated section in the README or a new markdown file for an attack profile development guide, explaining how to create and add new attack types.
    *   Document environment variable precedence rules and best practices for configuration.