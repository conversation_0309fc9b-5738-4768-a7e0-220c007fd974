# Project Plan: Agent Thinking Stream (Hyper-Detailed)

**Version:** 1.0
**Author:** <PERSON><PERSON>, Technical Leader
**Objective:** To enable and display the agent's real-time, token-by-token thinking process in the UI. This document serves as a complete and exhaustive guide for the implementation, providing all necessary context, code changes, and rationale.

---

## 1. Core Concept: Exposing the "Inner Monologue"

The primary goal is to make the agent's reasoning process transparent to the end-user. We are not just showing that the agent *is* thinking, but *what* it is thinking. To achieve this, we will leverage the built-in reasoning capabilities of the Agno framework and pipe the resulting thought process through our existing Server-Sent Events (SSE) stream to the UI.

The implementation is divided into two main phases:
1.  **Backend Modification:** Configuring the agent to produce a verbose stream of its reasoning and ensuring our streaming workflow correctly captures and forwards this stream.
2.  **Frontend Modification:** Updating the UI to receive, parse, and render this new "thinking" data in a way that is both informative and visually distinct.

---

## 2. Phase 1: Backend Implementation (Python/Agno)

**Primary File to Modify:** `agent/streaming_workflow.py`
**Supporting File (for context):** `api/schemas/stream_events.py`

### 2.1. Enabling Full Reasoning in the Agno Agent

**Context:** The Agno `Agent` class has parameters that control its reasoning and streaming behavior. To get the detailed "inner monologue," we must explicitly instruct the agent to enable its reasoning engine and to stream the *full* output of that engine, not just the final result.

**Action:** In `agent/streaming_workflow.py`, locate the `stream_agent_response` method. Within this method, find the calls to `self.agent.arun` (for asynchronous execution) and `self.agent.run` (for synchronous execution).

**Location (approximate):** Lines 78-89.

**Required Code Modification:** Add the `reasoning=True` and `show_full_reasoning=True` parameters to both the `arun` and `run` calls.

```python
# --- BEFORE ---
# Located in agent/streaming_workflow.py, around line 78
agent_stream = await self.agent.arun(
    query, 
    stream=True, 
    stream_intermediate_steps=True
)

# --- AFTER ---
# The new code with detailed comments explaining each parameter.
# This change must be applied to both the `arun` and `run` calls.
agent_stream = await self.agent.arun(
    query, 
    stream=True,  # Essential for getting a stream of events back.
    stream_intermediate_steps=True,  # Ensures we get events like tool calls.
    
    # --- ADD THE FOLLOWING TWO LINES ---
    reasoning=True,  # This activates the agent's chain-of-thought reasoning engine.
    show_full_reasoning=True  # This is the critical parameter that tells Agno to stream the raw "thinking" content.
)
```

### 2.2. Capturing and Streaming the Real Reasoning Content

**Context:** The `_process_single_event` method (lines 235-340) is the heart of our event processing logic. It receives events directly from the Agno stream and transforms them into our custom `StreamEvent` format. The documentation confirms that when `show_full_reasoning=True`, Agno will yield `ReasoningStep` events containing the agent's thoughts.

**Action:** We must ensure that our handler for `event_type == "ReasoningStep"` correctly extracts the raw text from the event and packages it for the frontend.

**Location (approximate):** Lines 301-313.

**Verification (No Code Change Needed, but Rationale is Critical):**
The existing code is already correctly implemented to handle this, but it's vital to understand *why* it works:

```python
# Located in agent/streaming_workflow.py, around line 301
elif event_type == "ReasoningStep":
    # This block is triggered when the Agno agent yields a thought in its reasoning chain.
    
    # `getattr(event, 'content', '')` extracts the actual text of the agent's thought process.
    # This is the "inner monologue" we want to display.
    reasoning_content = getattr(event, 'content', '')
    
    # `getattr(event, 'reasoning_content', '')` can sometimes provide additional structured detail.
    detailed_reasoning = getattr(event, 'reasoning_content', '')
    
    # We create our custom `ReasoningStepData` object.
    # The `step_type` is hardcoded to "reasoning" for now, but the frontend will differentiate based on content.
    # The crucial part is that `message` is populated with the real `reasoning_content`.
    yield StreamEvent(
        event=EventTypes.REASONING_STEP,
        data=ReasoningStepData(
            step_type="reasoning", # Note: We will handle the specific "thinking" type on the frontend.
            message=str(reasoning_content),
            detailed_reasoning=str(detailed_reasoning) if detailed_reasoning else None
        ).model_dump()
    )
```
*Self-correction during planning: The backend should ideally set the `step_type` to `'thinking'`. Let's refine the plan to do that.*

**Revised Action for 2.2:**
Modify the `_process_single_event` method to intelligently set the `step_type`.

```python
# --- REVISED ACTION for agent/streaming_workflow.py, around line 301 ---
elif event_type == "ReasoningStep":
    reasoning_content = getattr(event, 'content', '')
    detailed_reasoning = getattr(event, 'reasoning_content', '')
    
    # Determine the step type. For our purposes, all raw reasoning is 'thinking'.
    # In a more complex scenario, we might parse the content for keywords.
    current_step_type = "thinking" 

    yield StreamEvent(
        event=EventTypes.REASONING_STEP,
        data=ReasoningStepData(
            step_type=current_step_type, # Use the determined step type.
            message=str(reasoning_content),
            detailed_reasoning=str(detailed_reasoning) if detailed_reasoning else None
        ).model_dump()
    )
```

---

## 3. Phase 2: Frontend Implementation (HTML/JavaScript)

**File to Modify:** `demo_streaming_ui.html`

### 3.1. Add a "Thinking" Icon

**Context:** The UI uses a dictionary (`stepIcon`) to map event subtypes to visual icons. We need to add an entry for our new `'thinking'` step type.

**Action:** Locate the `stepIcon` dictionary in the `addEvent` function and add a new key-value pair.

**Location (approximate):** Line 451.

**Required Code Modification:**

```javascript
// Located in demo_streaming_ui.html, inside the addEvent function
const stepIcon = {
    'knowledge_search': '🔍',
    'web_search': '🌐',
    'reasoning': '🧠',
    'reasoning_started': '🧠',
    'reasoning_completed': '✅',
    'thinking': '🤔', // <-- ADD THIS LINE
    'analyzing': '📊',
    'synthesizing': '🔗',
    'finalizing': '🎯'
}[data.step_type] || '⚡';
```

### 3.2. Add Custom Styling for "Thinking" Events

**Context:** To make the agent's thoughts visually distinct, we will create a new CSS class that applies a unique background and border color. We will also style the text to look like a "raw" thought process, using a monospace font.

**Action:** Add a new CSS rule inside the `<style>` block.

**Location (approximate):** Line 126 (after the existing `.event.reasoning-step` rule).

**Required Code Modification:**

```css
/* Located in demo_streaming_ui.html, inside the <style> block */
.event.reasoning-step.thinking {
    background: #f0e6ff; /* A very light, soft purple */
    border-left-color: #9575cd; /* A medium purple, less harsh than deep purple */
    margin: 10px 0; /* Add some vertical space to make thoughts stand out */
}

/* Style the text within the thinking block to look like a raw thought or code snippet */
.event.reasoning-step.thinking .reasoning-message {
    font-family: 'Courier New', Courier, monospace;
    font-style: normal;
    color: #444; /* Dark gray instead of pure black for softer text */
    white-space: pre-wrap; /* Preserve whitespace and line breaks from the agent's thoughts */
    font-size: 0.9em;
}
```

### 3.3. Apply the Custom Class to Render Thinking Events

**Context:** The `addEvent` function dynamically creates the HTML for each event. We need to add logic to apply our new `.thinking` class when a corresponding event is received.

**Action:** Modify the `addEvent` function to add the `thinking` class to the `eventEl` element when the `step_type` is `'thinking'`.

**Location (approximate):** Line 368.

**Required Code Modification:**

```javascript
// Located in demo_streaming_ui.html, inside the addEvent function
eventEl.className = `event ${type.replace('_', '-')}`;

// --- ADD THIS IF-STATEMENT ---
// This logic specifically targets our thinking steps to apply the custom CSS class.
if (type === 'reasoning_step' && data.step_type === 'thinking') {
    eventEl.classList.add('thinking');
}
```

---

## 4. Final Implementation Flow

This diagram visualizes the end-to-end flow of data, from the initial agent configuration to the final rendering in the UI.

```mermaid
graph TD
    subgraph "Backend (Python/Agno)"
        A[API Request: /chat/stream] --> B{Agent Initialization};
        B -- "reasoning=True, show_full_reasoning=True" --> C[Agent Execution];
        C --> D{Agno Event Stream};
        D -- "yields ReasoningStep event" --> E[Extract Real Reasoning Text from event.content];
        E --> F[Create ReasoningStepData with step_type='thinking' and the real text];
        F --> G[Yield SSE to Client];
    end

    subgraph "Frontend (JavaScript)"
        H[Receive SSE Event] --> I{Parse Event};
        I --> J{addEvent(type, data)};
        J -- "type == 'reasoning_step' && data.step_type == 'thinking'" --> K{Render data.message into .reasoning-message div};
        K --> L[Add .thinking class to parent event element];
        L --> M[UI Displays Actual Thinking Text with '🤔' Icon & Custom Purple Style];
    end
```

This hyper-detailed plan provides a complete, step-by-step guide with full context, ensuring that the implementing agent has all the information it needs to successfully complete the task.