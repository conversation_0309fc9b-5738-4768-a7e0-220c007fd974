# Streaming API Usage Guide

## Overview

The cybersecurity threat intelligence agent now provides a beautiful, real-time streaming API that shows every detail of the agent's execution process. Perfect for creating informational interfaces that display:

- Agent initialization and configuration
- LLM token-by-token responses
- Tool call executions with structured results
- Search results with titles, URLs, and snippets
- Knowledge base document previews
- Agent reasoning and intermediate steps
- Completion status and metrics

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the API Server

```bash
python main.py
```

The API will be available at `http://localhost:8000`

### 3. API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

## Streaming Endpoint

### POST `/api/v1/chat/stream`

Stream agent responses in real-time using Server-Sent Events (SSE).

**Request Body:**
```json
{
  "query": "How can I detect SQL injection attacks?",
  "attack_type": "sqli",
  "session_id": "optional-session-id"
}
```

**Response:** Server-Sent Events stream with the following event types:

## Event Types

### 1. `agent_start`
Agent initialization event with configuration details.

```json
{
  "event": "agent_start",
  "data": {
    "attack_type": "sqli",
    "display_name": "SQL Injection Research Agent",
    "knowledge_base": "sqli_collection",
    "specialized_terms": ["sql injection", "sqli", "union attack"],
    "session_id": "uuid-here"
  }
}
```

### 2. `llm_token`
Individual LLM tokens as they're generated.

```json
{
  "event": "llm_token",
  "data": {
    "token": "SQL injection attacks",
    "is_complete": false
  }
}
```

### 3. `tool_call_start`
Tool execution begins.

```json
{
  "event": "tool_call_start",
  "data": {
    "tool_name": "web_search",
    "tool_input": {
      "query": "latest SQL injection detection methods 2024",
      "num_results": 5
    },
    "call_id": "uuid-here"
  }
}
```

### 4. `tool_call_result`
Structured tool results perfect for UI display.

**Web Search Results:**
```json
{
  "event": "tool_call_result",
  "data": {
    "tool_name": "web_search",
    "success": true,
    "search_results": [
      {
        "title": "Advanced SQL Injection Detection Techniques",
        "url": "https://example.com/sqli-detection",
        "snippet": "Modern approaches to detecting SQL injection...",
        "has_full_content": true
      }
    ],
    "execution_time_ms": 1234.5
  }
}
```

**Knowledge Base Results:**
```json
{
  "event": "tool_call_result",
  "data": {
    "tool_name": "search_knowledge_base",
    "success": true,
    "knowledge_documents": [
      {
        "source": "owasp_sqli_guide.pdf",
        "content_preview": "SQL injection is a code injection technique...",
        "metadata": {
          "title": "OWASP SQL Injection Guide",
          "author": "OWASP Foundation"
        },
        "relevance_score": 0.95
      }
    ]
  }
}
```

### 5. `reasoning_step`
Agent reasoning and intermediate steps.

```json
{
  "event": "reasoning_step",
  "data": {
    "step_type": "thinking",
    "message": "Analyzing the search results to identify key detection patterns...",
    "confidence": 0.8
  }
}
```

### 6. `agent_complete`
Final completion event with metrics.

```json
{
  "event": "agent_complete",
  "data": {
    "final_answer": "Complete response here...",
    "total_time_ms": 15000.0,
    "tools_used": ["web_search", "search_knowledge_base"]
  }
}
```

### 7. `error`
Error events for handling issues.

```json
{
  "event": "error",
  "data": {
    "message": "Error description",
    "error_type": "TimeoutError",
    "code": 500,
    "recoverable": true
  }
}
```

## Client Implementation Examples

### JavaScript/TypeScript Frontend

```javascript
const eventSource = new EventSource('/api/v1/chat/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    query: "How can I detect SQL injection attacks?",
    attack_type: "sqli"
  })
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  switch(event.type) {
    case 'agent_start':
      showAgentInitialization(data);
      break;
    case 'llm_token':
      appendTokenToResponse(data.token);
      break;
    case 'tool_call_start':
      showToolExecution(data.tool_name, data.tool_input);
      break;
    case 'tool_call_result':
      displayToolResults(data);
      break;
    case 'reasoning_step':
      showAgentThinking(data.message);
      break;
    case 'agent_complete':
      showCompletion(data);
      break;
    case 'error':
      handleError(data);
      break;
  }
};
```

### Python Client

```python
import requests
import json

def stream_chat(query, attack_type="sqli"):
    url = "http://localhost:8000/api/v1/chat/stream"
    data = {
        "query": query,
        "attack_type": attack_type
    }
    
    response = requests.post(url, json=data, stream=True)
    
    for line in response.iter_lines():
        if line:
            # Parse SSE format
            if line.startswith(b'data: '):
                event_data = json.loads(line[6:])
                yield event_data

# Usage
for event in stream_chat("How can I detect SQL injection attacks?"):
    print(f"Event: {event}")
```

## Available Attack Types

- `sqli` - SQL Injection specialization
- `xss` - Cross-Site Scripting specialization  
- `rce` - Remote Code Execution specialization

## Additional Endpoints

### GET `/api/v1/chat/models`
List available attack types and their configurations.

### GET `/api/v1/chat/health`
Health check endpoint.

### POST `/api/v1/chat/validate`
Validate a chat request without executing it.

## Environment Configuration

Make sure your `.env` file is properly configured with:

```env
ATTACK_PROFILE=sqli
QDRANT_URL=your-qdrant-url
QDRANT_API_KEY=your-api-key
AZURE_OPENAI_ENDPOINT=your-endpoint
AZURE_OPENAI_API_KEY=your-key
# ... other required variables
```

## Error Handling

The API provides structured error responses with:
- Error message and type
- HTTP status codes
- Recovery suggestions
- Detailed logging

## Performance Notes

- Events are streamed with small delays (10ms) to prevent overwhelming clients
- Tool results are structured for optimal UI display
- Client disconnection is properly handled
- Agent instances are cached for better performance

## UI Integration Tips

1. **Token Streaming**: Build up the response character by character for typewriter effect
2. **Tool Results**: Display search results as cards with titles, URLs, and snippets
3. **Knowledge Docs**: Show document previews with source attribution
4. **Progress Indicators**: Use reasoning steps to show agent thinking process
5. **Error Recovery**: Implement graceful error handling with retry options